"""
Pydantic schemas for request/response models in Expendra API.
"""
from datetime import datetime
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, EmailStr, Field

# User Schemas
class UserBase(BaseModel):
    email: EmailStr
    username: str = Field(..., min_length=3, max_length=50)
    full_name: Optional[str] = None

class UserCreate(UserBase):
    password: str = Field(..., min_length=8, max_length=100)

class UserUpdate(BaseModel):
    email: Optional[EmailStr] = None
    username: Optional[str] = Field(None, min_length=3, max_length=50)
    full_name: Optional[str] = None

class UserResponse(UserBase):
    id: int
    is_active: bool
    is_verified: bool
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

# Authentication Schemas
class LoginRequest(BaseModel):
    email: EmailStr
    password: str

class TokenResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"
    expires_in: int
    user: UserResponse

# User Settings Schemas
class UserSettingsBase(BaseModel):
    # LLM Provider Settings
    default_llm_provider: str = Field(default="openai", pattern="^(openai|anthropic|google|ollama)$")
    openai_api_key: Optional[str] = None
    anthropic_api_key: Optional[str] = None
    google_api_key: Optional[str] = None
    ollama_endpoint: Optional[str] = None
    default_model: str = "gpt-3.5-turbo"
    
    # UI/UX Settings
    theme: str = Field(default="dark", pattern="^(dark|light)$")
    primary_color: str = "blue"
    font_size: str = Field(default="medium", pattern="^(small|medium|large)$")
    animations_enabled: bool = True
    
    # Research Preferences
    default_research_depth: str = Field(default="standard", pattern="^(quick|standard|deep)$")
    auto_export_format: str = Field(default="pdf", pattern="^(pdf|excel|both)$")

class UserSettingsCreate(UserSettingsBase):
    pass

class UserSettingsUpdate(BaseModel):
    # LLM Provider Settings
    default_llm_provider: Optional[str] = Field(None, pattern="^(openai|anthropic|google|ollama)$")
    openai_api_key: Optional[str] = None
    anthropic_api_key: Optional[str] = None
    google_api_key: Optional[str] = None
    ollama_endpoint: Optional[str] = None
    default_model: Optional[str] = None
    
    # UI/UX Settings
    theme: Optional[str] = Field(None, pattern="^(dark|light)$")
    primary_color: Optional[str] = None
    font_size: Optional[str] = Field(None, pattern="^(small|medium|large)$")
    animations_enabled: Optional[bool] = None
    
    # Research Preferences
    default_research_depth: Optional[str] = Field(None, pattern="^(quick|standard|deep)$")
    auto_export_format: Optional[str] = Field(None, pattern="^(pdf|excel|both)$")

class UserSettingsResponse(UserSettingsBase):
    id: int
    user_id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

# Research Session Schemas
class ResearchSessionBase(BaseModel):
    title: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    query: str = Field(..., min_length=1)
    priority: str = Field(default="normal", pattern="^(low|normal|high|urgent)$")

class ResearchSessionCreate(ResearchSessionBase):
    context_data: Optional[Dict[str, Any]] = None

class ResearchSessionUpdate(BaseModel):
    title: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    status: Optional[str] = Field(None, pattern="^(pending|in_progress|completed|failed)$")
    priority: Optional[str] = Field(None, pattern="^(low|normal|high|urgent)$")

class ResearchSessionResponse(ResearchSessionBase):
    id: int
    user_id: int
    status: str
    context_data: Optional[Dict[str, Any]] = None
    research_results: Optional[Dict[str, Any]] = None
    estimated_duration: Optional[int] = None
    actual_duration: Optional[int] = None
    created_at: datetime
    updated_at: datetime
    completed_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

# Research Finding Schemas
class ResearchFindingBase(BaseModel):
    finding_type: str = Field(..., min_length=1, max_length=50)
    title: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    data: Dict[str, Any]
    source_url: Optional[str] = None
    source_name: Optional[str] = None
    confidence_score: int = Field(default=0, ge=0, le=100)

class ResearchFindingCreate(ResearchFindingBase):
    session_id: int

class ResearchFindingResponse(ResearchFindingBase):
    id: int
    session_id: int
    created_at: datetime
    
    class Config:
        from_attributes = True

# API Response Schemas
class MessageResponse(BaseModel):
    message: str
    success: bool = True

class ErrorResponse(BaseModel):
    detail: str
    error_code: Optional[str] = None
    success: bool = False
