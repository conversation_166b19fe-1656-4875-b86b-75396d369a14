// API service for Expendra frontend
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api/v1'

interface LoginResponse {
  access_token: string
  token_type: string
  expires_in: number
  user: {
    id: number
    email: string
    username: string
    full_name?: string
    is_active: boolean
    is_verified: boolean
    created_at: string
    updated_at: string
  }
}

interface User {
  id: number
  email: string
  username: string
  full_name?: string
  is_active: boolean
  is_verified: boolean
  created_at: string
  updated_at: string
}

interface UserSettings {
  id: number
  user_id: number
  default_llm_provider: string
  openai_api_key?: string
  anthropic_api_key?: string
  google_api_key?: string
  ollama_endpoint?: string
  default_model: string
  theme: string
  primary_color: string
  font_size: string
  animations_enabled: boolean
  default_research_depth: string
  auto_export_format: string
  created_at: string
  updated_at: string
}

class APIError extends Error {
  constructor(message: string, public status?: number) {
    super(message)
    this.name = 'APIError'
  }
}

const handleResponse = async (response: Response) => {
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ detail: 'Unknown error' }))
    throw new APIError(errorData.detail || 'Request failed', response.status)
  }
  return response.json()
}

const makeRequest = async (
  endpoint: string,
  options: RequestInit = {},
  token?: string
) => {
  const url = `${API_BASE_URL}${endpoint}`
  const headers: HeadersInit = {
    'Content-Type': 'application/json',
    ...options.headers,
  }

  if (token) {
    headers.Authorization = `Bearer ${token}`
  }

  const response = await fetch(url, {
    ...options,
    headers,
  })

  return handleResponse(response)
}

export const authAPI = {
  async login(email: string, password: string): Promise<LoginResponse> {
    return makeRequest('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    })
  },

  async register(
    email: string,
    username: string,
    password: string,
    full_name?: string
  ): Promise<User> {
    return makeRequest('/auth/register', {
      method: 'POST',
      body: JSON.stringify({ email, username, password, full_name }),
    })
  },

  async getCurrentUser(token: string): Promise<User> {
    return makeRequest('/auth/me', { method: 'GET' }, token)
  },

  async refreshToken(token: string): Promise<LoginResponse> {
    return makeRequest('/auth/refresh', { method: 'POST' }, token)
  },

  async logout(token: string): Promise<{ message: string; success: boolean }> {
    return makeRequest('/auth/logout', { method: 'POST' }, token)
  },
}

export const settingsAPI = {
  async getUserSettings(token: string): Promise<UserSettings> {
    return makeRequest('/settings/', { method: 'GET' }, token)
  },

  async updateUserSettings(
    token: string,
    settings: Partial<UserSettings>
  ): Promise<UserSettings> {
    return makeRequest('/settings/', {
      method: 'PUT',
      body: JSON.stringify(settings),
    }, token)
  },

  async resetUserSettings(token: string): Promise<{ message: string; success: boolean }> {
    return makeRequest('/settings/reset', { method: 'POST' }, token)
  },

  async getAvailableLLMProviders(): Promise<{
    providers: Record<string, any>
    default_provider: string
    default_model: string
  }> {
    return makeRequest('/settings/llm-providers', { method: 'GET' })
  },

  async getAvailableModels(): Promise<{
    status: string
    models: Record<string, { name: string; context_length: number; description?: string }>
    error?: string
  }> {
    return makeRequest('/settings/available-models', { method: 'GET' })
  },
}

// Research API interfaces
interface ResearchSession {
  id: string
  query: string
  user_id: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  created_at: string
  results?: ResearchResult[]
}

interface ResearchResult {
  type: string
  content: string
  timestamp: string
  metadata?: Record<string, any>
}

interface StartResearchRequest {
  query: string
  context_data?: Record<string, any>
  priority?: 'low' | 'normal' | 'high' | 'urgent'
  research_depth?: 'quick' | 'standard' | 'deep'
}

interface ConnectionTestResponse {
  status: 'success' | 'error'
  llm_service?: {
    status: string
    provider: string
    model: string
    message: string
  }
  message: string
}

export const researchAPI = {
  // Test connection to research services
  async testConnection(): Promise<ConnectionTestResponse> {
    return makeRequest('/research/test-connection-public', { method: 'GET' })
  },

  // Start a new research session (public endpoint for testing)
  async startResearchSession(request: StartResearchRequest): Promise<{
    session_id: string
    status: string
    message: string
  }> {
    return makeRequest('/research/start-public', {
      method: 'POST',
      body: JSON.stringify(request),
    })
  },

  // Get research session status (public endpoint for testing)
  async getSessionStatus(sessionId: string): Promise<ResearchSession> {
    return makeRequest(`/research/sessions/${sessionId}/status-public`, { method: 'GET' })
  },

  // Start authenticated research session
  async startAuthenticatedResearchSession(
    token: string,
    request: StartResearchRequest
  ): Promise<{
    session_id: string
    status: string
    message: string
  }> {
    return makeRequest('/research/start', {
      method: 'POST',
      body: JSON.stringify(request),
    }, token)
  },

  // Get authenticated session status
  async getAuthenticatedSessionStatus(token: string, sessionId: string): Promise<ResearchSession> {
    return makeRequest(`/research/sessions/${sessionId}/status`, { method: 'GET' }, token)
  },

  // Get all user sessions
  async getUserSessions(token: string): Promise<ResearchSession[]> {
    return makeRequest('/research/sessions', { method: 'GET' }, token)
  },
}

// Dashboard API interfaces
interface DashboardMetrics {
  totalSearches: {
    value: number
    trend: { value: number; isPositive: boolean; label: string }
  }
  activeResearch: {
    value: number
    progress: { value: number; max: number; color: string }
  }
  reportsGenerated: {
    value: number
    trend: { value: number; isPositive: boolean; label: string }
  }
  avgResponseTime: {
    value: string
    trend: { value: number; isPositive: boolean; label: string }
  }
}

interface ActivityItem {
  id: string
  type: 'search' | 'research'
  title: string
  description: string
  timestamp: string
  user: { name: string }
  metadata: Record<string, any>
}

interface ChartDataPoint {
  value: number
}

export const dashboardAPI = {
  // Get dashboard metrics
  async getMetrics(token: string): Promise<DashboardMetrics> {
    return makeRequest('/dashboard/metrics', { method: 'GET' }, token)
  },

  // Get dashboard activity
  async getActivity(token: string, limit: number = 10): Promise<ActivityItem[]> {
    return makeRequest(`/dashboard/activity?limit=${limit}`, { method: 'GET' }, token)
  },

  // Get chart data
  async getChartData(token: string, days: number = 30): Promise<ChartDataPoint[]> {
    return makeRequest(`/dashboard/chart-data?days=${days}`, { method: 'GET' }, token)
  },
}

export { APIError }
