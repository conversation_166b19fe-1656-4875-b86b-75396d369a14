"""
Dashboard routes for Expendra API.
"""
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import func, desc

from database import get_db
from models import User, ResearchSession
from auth import get_current_active_user

router = APIRouter(prefix="/dashboard", tags=["Dashboard"])

@router.get("/metrics")
async def get_dashboard_metrics(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get dashboard metrics for the current user.
    """
    # Calculate date ranges
    now = datetime.utcnow()
    last_month = now - timedelta(days=30)
    last_week = now - timedelta(days=7)
    
    # Get total searches (research sessions) for the user
    total_searches = db.query(ResearchSession).filter(
        ResearchSession.user_id == current_user.id,
        ResearchSession.created_at >= last_month
    ).count()
    
    # Get searches from previous month for trend calculation
    prev_month_start = last_month - timedelta(days=30)
    prev_month_searches = db.query(ResearchSession).filter(
        ResearchSession.user_id == current_user.id,
        ResearchSession.created_at >= prev_month_start,
        ResearchSession.created_at < last_month
    ).count()
    
    # Calculate trend
    search_trend = 0.0
    if prev_month_searches > 0:
        search_trend = ((total_searches - prev_month_searches) / prev_month_searches) * 100
    elif total_searches > 0:
        search_trend = 100.0  # If no previous data but current data exists
    
    # Get active research (in progress sessions)
    active_research = db.query(ResearchSession).filter(
        ResearchSession.user_id == current_user.id,
        ResearchSession.status.in_(["pending", "in_progress"])
    ).count()
    
    # Get completed research sessions this week
    reports_generated = db.query(ResearchSession).filter(
        ResearchSession.user_id == current_user.id,
        ResearchSession.status == "completed",
        ResearchSession.created_at >= last_week
    ).count()
    
    # Get reports from previous week for trend
    prev_week_start = last_week - timedelta(days=7)
    prev_week_reports = db.query(ResearchSession).filter(
        ResearchSession.user_id == current_user.id,
        ResearchSession.status == "completed",
        ResearchSession.created_at >= prev_week_start,
        ResearchSession.created_at < last_week
    ).count()
    
    # Calculate reports trend
    reports_trend = 0.0
    if prev_week_reports > 0:
        reports_trend = ((reports_generated - prev_week_reports) / prev_week_reports) * 100
    elif reports_generated > 0:
        reports_trend = 100.0
    
    # Calculate average response time (mock for now, could be calculated from session duration)
    avg_response_time = "2.3s"
    response_time_trend = -15.3  # Mock improvement
    
    return {
        "totalSearches": {
            "value": total_searches,
            "trend": {
                "value": round(search_trend, 1),
                "isPositive": search_trend >= 0,
                "label": "vs last month"
            }
        },
        "activeResearch": {
            "value": active_research,
            "progress": {
                "value": min(65, (active_research / max(1, total_searches)) * 100),
                "max": 100,
                "color": "#8B5CF6"
            }
        },
        "reportsGenerated": {
            "value": reports_generated,
            "trend": {
                "value": round(reports_trend, 1),
                "isPositive": reports_trend >= 0,
                "label": "this week"
            }
        },
        "avgResponseTime": {
            "value": avg_response_time,
            "trend": {
                "value": response_time_trend,
                "isPositive": response_time_trend < 0,  # Negative is good for response time
                "label": "improvement"
            }
        }
    }

@router.get("/activity")
async def get_dashboard_activity(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
    limit: int = 10
):
    """
    Get recent activity for the dashboard.
    """
    # Get recent research sessions
    recent_sessions = db.query(ResearchSession).filter(
        ResearchSession.user_id == current_user.id
    ).order_by(desc(ResearchSession.created_at)).limit(limit).all()
    
    activity_data = []
    for session in recent_sessions:
        # Calculate time ago
        time_diff = datetime.utcnow() - session.created_at
        if time_diff.days > 0:
            time_ago = f"{time_diff.days} day{'s' if time_diff.days > 1 else ''} ago"
        elif time_diff.seconds > 3600:
            hours = time_diff.seconds // 3600
            time_ago = f"{hours} hour{'s' if hours > 1 else ''} ago"
        elif time_diff.seconds > 60:
            minutes = time_diff.seconds // 60
            time_ago = f"{minutes} min ago"
        else:
            time_ago = "Just now"
        
        # Determine activity type and details based on status
        if session.status == "completed":
            activity_type = "search"
            title = "Part Search Completed"
            description = f"Research completed: {session.title[:50]}..."
            metadata = {"status": "completed", "count": 1}
        elif session.status in ["pending", "in_progress"]:
            activity_type = "research"
            title = "Market Research Started" if session.status == "in_progress" else "Research Queued"
            description = f"Analysis for: {session.title[:50]}..."
            metadata = {"status": "in-progress" if session.status == "in_progress" else "pending"}
        else:
            activity_type = "research"
            title = "Research Session"
            description = f"Status: {session.status} - {session.title[:50]}..."
            metadata = {"status": session.status}
        
        activity_data.append({
            "id": str(session.id),
            "type": activity_type,
            "title": title,
            "description": description,
            "timestamp": time_ago,
            "user": {"name": current_user.full_name or current_user.username},
            "metadata": metadata
        })
    
    return activity_data

@router.get("/chart-data")
async def get_dashboard_chart_data(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
    days: int = 30
):
    """
    Get chart data for dashboard analytics.
    """
    # Calculate date range
    end_date = datetime.utcnow().date()
    start_date = end_date - timedelta(days=days-1)
    
    # Get daily search counts
    daily_data = db.query(
        func.date(ResearchSession.created_at).label('date'),
        func.count(ResearchSession.id).label('count')
    ).filter(
        ResearchSession.user_id == current_user.id,
        func.date(ResearchSession.created_at) >= start_date,
        func.date(ResearchSession.created_at) <= end_date
    ).group_by(func.date(ResearchSession.created_at)).all()
    
    # Create a complete dataset with zeros for missing days
    chart_data = []
    data_dict = {str(row.date): row.count for row in daily_data}
    
    current_date = start_date
    while current_date <= end_date:
        count = data_dict.get(str(current_date), 0)
        chart_data.append({"value": count})
        current_date += timedelta(days=1)
    
    return chart_data
