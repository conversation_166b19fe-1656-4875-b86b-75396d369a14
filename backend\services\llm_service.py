"""
LLM Service for managing Google Gemini API using the new google-genai SDK.
"""
import os
from typing import Optional, Dict, Any, List
import asyncio
from dotenv import load_dotenv
from google import genai
from google.genai import types, errors
import logging

load_dotenv()
logger = logging.getLogger(__name__)

class LLMService:
    """
    Service for managing Google Gemini API using the new google-genai SDK.
    """

    def __init__(self):
        self.client = None
        self._initialize_client()

    def _initialize_client(self):
        """Initialize the Google GenAI client."""
        try:
            api_key = os.getenv("GOOGLE_API_KEY")
            if api_key:
                self.client = genai.Client(api_key=api_key)
                logger.info("Google GenAI client initialized successfully")
            else:
                logger.warning("GOOGLE_API_KEY not found in environment variables")
        except Exception as e:
            logger.error(f"Failed to initialize Google GenAI client: {e}")
            self.client = None

    async def generate_content(
        self,
        prompt: str,
        model: str = "gemini-2.0-flash-001",
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate content using Google Gemini API.

        Args:
            prompt: The input prompt
            model: Model name to use
            **kwargs: Additional configuration parameters

        Returns:
            Generated content response
        """
        if not self.client:
            raise ValueError("Google GenAI client not initialized. Check your API key.")

        try:
            # Configure generation parameters
            config = types.GenerateContentConfig(
                temperature=kwargs.get("temperature", 0.7),
                max_output_tokens=kwargs.get("max_tokens", 1000),
                top_p=kwargs.get("top_p", 0.9),
                top_k=kwargs.get("top_k", 40)
            )

            response = self.client.models.generate_content(
                model=model,
                contents=prompt,
                config=config
            )

            return {
                "status": "success",
                "content": response.text,
                "model": model,
                "usage": {
                    "prompt_tokens": len(prompt.split()),  # Rough estimate
                    "completion_tokens": len(response.text.split()) if response.text else 0
                }
            }

        except errors.APIError as e:
            logger.error(f"Gemini API error: {e.code} - {e.message}")
            return {
                "status": "error",
                "error": f"API Error {e.code}: {e.message}",
                "model": model
            }
        except Exception as e:
            logger.error(f"Unexpected error in generate_content: {e}")
            return {
                "status": "error",
                "error": str(e),
                "model": model
            }

    async def test_connection(self, model: str = "gemini-2.0-flash-001") -> Dict[str, Any]:
        """
        Test connection to Google Gemini API.

        Args:
            model: Model to test

        Returns:
            Test results
        """
        if not self.client:
            return {
                "status": "error",
                "provider": "google",
                "model": model,
                "error": "Client not initialized",
                "message": "Google GenAI client not initialized. Check your API key."
            }

        try:
            # Test with a simple prompt
            response = await self.generate_content(
                prompt="Hello, this is a connection test.",
                model=model
            )

            if response["status"] == "success":
                return {
                    "status": "success",
                    "provider": "google",
                    "model": model,
                    "message": "Connection successful",
                    "test_response": response["content"][:100] + "..." if len(response["content"]) > 100 else response["content"]
                }
            else:
                return {
                    "status": "error",
                    "provider": "google",
                    "model": model,
                    "error": response.get("error", "Unknown error"),
                    "message": f"Connection test failed: {response.get('error', 'Unknown error')}"
                }

        except Exception as e:
            logger.error(f"Connection test failed: {e}")
            return {
                "status": "error",
                "provider": "google",
                "model": model,
                "error": str(e),
                "message": f"Connection test failed: {str(e)}"
            }

    async def get_available_models(self) -> Dict[str, Any]:
        """
        Get available models from Google Gemini API.

        Returns:
            Dictionary with model information
        """
        if not self.client:
            # Return fallback models if client not initialized
            return {
                "status": "error",
                "error": "Client not initialized",
                "models": {
                    "gemini-2.0-flash-001": {"name": "Gemini 2.0 Flash", "context_length": 1000000},
                    "gemini-1.5-pro": {"name": "Gemini 1.5 Pro", "context_length": 2000000},
                    "gemini-1.5-flash": {"name": "Gemini 1.5 Flash", "context_length": 1000000}
                }
            }

        try:
            # List available models from the API
            models_response = self.client.models.list()

            models_dict = {}
            for model in models_response:
                # Extract model info
                model_id = model.name.split('/')[-1] if '/' in model.name else model.name
                models_dict[model_id] = {
                    "name": model.display_name or model_id,
                    "context_length": getattr(model, 'input_token_limit', 1000000),
                    "description": getattr(model, 'description', ''),
                    "supported_generation_methods": getattr(model, 'supported_generation_methods', [])
                }

            return {
                "status": "success",
                "models": models_dict
            }

        except Exception as e:
            logger.error(f"Failed to fetch available models: {e}")
            # Return fallback models on error
            return {
                "status": "error",
                "error": str(e),
                "models": {
                    "gemini-2.0-flash-001": {"name": "Gemini 2.0 Flash", "context_length": 1000000},
                    "gemini-1.5-pro": {"name": "Gemini 1.5 Pro", "context_length": 2000000},
                    "gemini-1.5-flash": {"name": "Gemini 1.5 Flash", "context_length": 1000000}
                }
            }

# Global LLM service instance
llm_service = LLMService()
