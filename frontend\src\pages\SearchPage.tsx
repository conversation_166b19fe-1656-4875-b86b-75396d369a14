import React, { useState, useRef, useEffect } from 'react'
import {
  Box,
  Container,
  Heading,
  Text,
  VStack,
  HStack,
  Button,
  Card,
  CardBody,
  Input,
  Textarea,
  IconButton,
  Badge,
  Flex,
  Spacer,
  Divider,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  useToast,
  Progress,
  Spinner,
  Avatar,
  Tooltip,
  SimpleGrid,
  Link,
  Image,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
} from '@chakra-ui/react'
import {
  FiSend,
  FiPaperclip,
  FiDownload,
  FiExternalLink,
  FiCopy,
  FiShare2,
  FiMoreVertical,
  FiFileText,
  FiImage,
  FiX,
  FiSearch,
  FiShoppingCart,
  FiTrendingUp,
  FiAlertTriangle,
  FiCheckCircle,
  FiClock,
  FiDollarSign,
  FiPackage,
  FiTool,
  FiSettings,
} from 'react-icons/fi'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'
import { researchAPI } from '../services/api'
import { Header } from '../components'

// Types for our search interface
interface Message {
  id: string
  type: 'user' | 'assistant' | 'system'
  content: string
  timestamp: Date
  attachments?: FileAttachment[]
  searchResults?: SearchResult[]
  isLoading?: boolean
}

interface FileAttachment {
  id: string
  name: string
  type: string
  size: number
  url?: string
}

interface SearchResult {
  id: string
  title: string
  description: string
  price?: string
  vendor: string
  partNumber?: string
  availability: 'in-stock' | 'limited' | 'out-of-stock' | 'unknown'
  confidence: 'high' | 'medium' | 'low'
  url: string
  image?: string
  specifications?: Record<string, string>
  alternatives?: string[]
  warnings?: string[]
}

interface ExportOption {
  type: 'pdf' | 'excel' | 'csv'
  label: string
  icon: React.ReactElement
}

// MessageBubble Component
interface MessageBubbleProps {
  message: Message
  onResultClick: (result: SearchResult) => void
  onPreviewOpen: () => void
  onPlaceOrder: (result: SearchResult) => void
  onCopyToClipboard: (text: string) => void
}

const MessageBubble: React.FC<MessageBubbleProps> = ({
  message,
  onResultClick,
  onPreviewOpen,
  onPlaceOrder,
  onCopyToClipboard,
}) => {
  const isUser = message.type === 'user'
  const isSystem = message.type === 'system'

  return (
    <Box display="flex" justifyContent={isUser ? 'flex-end' : 'flex-start'}>
      <Box maxW="70%" w="full">
        {!isUser && (
          <HStack spacing={2} mb={2}>
            <Avatar size="sm" bg="brand.500" color="white" name="AI" />
            <Text color="gray.400" fontSize="sm">
              {isSystem ? 'System' : 'AI Assistant'}
            </Text>
            <Text color="gray.500" fontSize="xs">
              {message.timestamp.toLocaleTimeString()}
            </Text>
          </HStack>
        )}

        <Card
          bg={isUser ? 'brand.600' : 'gray.800'}
          borderColor={isUser ? 'brand.500' : 'gray.700'}
          variant={isUser ? 'solid' : 'outline'}
        >
          <CardBody>
            {/* Message Content */}
            <Text color={isUser ? 'white' : 'gray.100'} mb={message.attachments || message.searchResults ? 3 : 0}>
              {message.content}
            </Text>

            {/* Attachments */}
            {message.attachments && message.attachments.length > 0 && (
              <VStack spacing={2} align="start" mb={3}>
                {message.attachments.map((attachment) => (
                  <HStack key={attachment.id} spacing={2}>
                    {attachment.type.startsWith('image/') ? (
                      <FiImage color="gray.400" />
                    ) : (
                      <FiFileText color="gray.400" />
                    )}
                    <Text color="gray.300" fontSize="sm">
                      {attachment.name}
                    </Text>
                    <Text color="gray.500" fontSize="xs">
                      ({(attachment.size / 1024).toFixed(1)} KB)
                    </Text>
                  </HStack>
                ))}
              </VStack>
            )}

            {/* Search Results */}
            {message.searchResults && message.searchResults.length > 0 && (
              <VStack spacing={4} align="stretch">
                {message.searchResults.map((result) => (
                  <SearchResultCard
                    key={result.id}
                    result={result}
                    onPreview={() => {
                      onResultClick(result)
                      onPreviewOpen()
                    }}
                    onPlaceOrder={() => onPlaceOrder(result)}
                    onCopyToClipboard={onCopyToClipboard}
                  />
                ))}
              </VStack>
            )}
          </CardBody>
        </Card>

        {isUser && (
          <HStack spacing={2} mt={2} justify="flex-end">
            <Text color="gray.500" fontSize="xs">
              {message.timestamp.toLocaleTimeString()}
            </Text>
            <Text color="gray.400" fontSize="sm">
              You
            </Text>
          </HStack>
        )}
      </Box>
    </Box>
  )
}

// SearchResultCard Component
interface SearchResultCardProps {
  result: SearchResult
  onPreview: () => void
  onPlaceOrder: () => void
  onCopyToClipboard: (text: string) => void
}

const SearchResultCard: React.FC<SearchResultCardProps> = ({
  result,
  onPreview,
  onPlaceOrder,
  onCopyToClipboard,
}) => {
  return (
    <Card bg="gray.750" borderColor="gray.600" size="sm">
      <CardBody>
        <Flex>
          {/* Result Image */}
          {result.image && (
            <Image
              src={result.image}
              alt={result.title}
              boxSize="80px"
              objectFit="cover"
              borderRadius="md"
              mr={4}
            />
          )}

          {/* Result Content */}
          <Box flex="1">
            <HStack justify="space-between" align="start" mb={2}>
              <VStack align="start" spacing={1}>
                <Text color="gray.100" fontWeight="semibold" fontSize="sm">
                  {result.title}
                </Text>
                <HStack spacing={2}>
                  <Badge
                    colorScheme={
                      result.confidence === 'high' ? 'green' :
                      result.confidence === 'medium' ? 'yellow' : 'red'
                    }
                    size="sm"
                  >
                    {result.confidence} confidence
                  </Badge>
                  <Badge
                    colorScheme={
                      result.availability === 'in-stock' ? 'green' :
                      result.availability === 'limited' ? 'yellow' : 'red'
                    }
                    size="sm"
                  >
                    {result.availability}
                  </Badge>
                </HStack>
              </VStack>

              <Menu>
                <MenuButton
                  as={IconButton}
                  icon={<FiMoreVertical />}
                  variant="ghost"
                  size="sm"
                  color="gray.400"
                />
                <MenuList bg="gray.800" borderColor="gray.700">
                  <MenuItem
                    icon={<FiExternalLink />}
                    onClick={() => window.open(result.url, '_blank')}
                    bg="gray.800"
                    _hover={{ bg: 'gray.700' }}
                    color="gray.100"
                  >
                    View on Site
                  </MenuItem>
                  <MenuItem
                    icon={<FiCopy />}
                    onClick={() => onCopyToClipboard(result.partNumber || '')}
                    bg="gray.800"
                    _hover={{ bg: 'gray.700' }}
                    color="gray.100"
                  >
                    Copy Part Number
                  </MenuItem>
                  <MenuItem
                    icon={<FiShare2 />}
                    onClick={() => onCopyToClipboard(result.url)}
                    bg="gray.800"
                    _hover={{ bg: 'gray.700' }}
                    color="gray.100"
                  >
                    Share Link
                  </MenuItem>
                </MenuList>
              </Menu>
            </HStack>

            <Text color="gray.300" fontSize="sm" mb={3} noOfLines={2}>
              {result.description}
            </Text>

            <HStack justify="space-between" align="center">
              <VStack align="start" spacing={1}>
                <Text color="gray.400" fontSize="xs">
                  {result.vendor} • {result.partNumber}
                </Text>
                {result.price && (
                  <Text color="brand.300" fontWeight="bold" fontSize="lg">
                    {result.price}
                  </Text>
                )}
              </VStack>

              <HStack spacing={2}>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={onPreview}
                  leftIcon={<FiSearch />}
                >
                  Details
                </Button>
                <Button
                  size="sm"
                  colorScheme="brand"
                  onClick={onPlaceOrder}
                  leftIcon={<FiShoppingCart />}
                >
                  Order
                </Button>
              </HStack>
            </HStack>

            {/* Warnings */}
            {result.warnings && result.warnings.length > 0 && (
              <Alert status="warning" size="sm" mt={3} bg="orange.900" borderColor="orange.700">
                <AlertIcon />
                <AlertDescription fontSize="xs">
                  {result.warnings[0]}
                </AlertDescription>
              </Alert>
            )}
          </Box>
        </Flex>
      </CardBody>
    </Card>
  )
}

const SearchPage = () => {
  const navigate = useNavigate()
  const { token } = useAuth()
  const toast = useToast()
  const { isOpen: isPreviewOpen, onOpen: onPreviewOpen, onClose: onPreviewClose } = useDisclosure()

  // State management
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'system',
      content: 'Hi! How can I help you find parts, tools, or materials today? You can upload files for context or ask me anything in natural language.',
      timestamp: new Date(),
    }
  ])
  const [inputValue, setInputValue] = useState('')
  const [attachments, setAttachments] = useState<FileAttachment[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [selectedResult, setSelectedResult] = useState<SearchResult | null>(null)
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null)

  // Refs
  const fileInputRef = useRef<HTMLInputElement>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  // Export options
  const exportOptions: ExportOption[] = [
    { type: 'pdf', label: 'Export as PDF', icon: <FiFileText /> },
    { type: 'excel', label: 'Export as Excel', icon: <FiDownload /> },
    { type: 'csv', label: 'Export as CSV', icon: <FiDownload /> },
  ]

  // Sample search results for demonstration
  const sampleResults: SearchResult[] = [
    {
      id: '1',
      title: 'Boeing 737 Hydraulic Filter Assembly',
      description: 'OEM hydraulic filter assembly compatible with Boeing 737-800 series. FAA PMA approved alternative available.',
      price: '$2,847.50',
      vendor: 'SkyGeek',
      partNumber: 'B737-HYD-001',
      availability: 'in-stock',
      confidence: 'high',
      url: 'https://skygeek.com/b737-hyd-001',
      image: '/api/placeholder/200/150',
      specifications: {
        'Material': 'Aluminum Alloy',
        'Weight': '2.3 lbs',
        'Dimensions': '8" x 6" x 4"',
        'Certification': 'FAA PMA'
      },
      alternatives: ['ALT-B737-HYD-002', 'GEN-HYD-737-A'],
    },
    {
      id: '2',
      title: 'Generic Hydraulic Filter - Compatible',
      description: 'Third-party hydraulic filter compatible with Boeing 737 series. Cost-effective alternative with similar specifications.',
      price: '$1,245.00',
      vendor: 'PartsBase',
      partNumber: 'GEN-HYD-737-B',
      availability: 'limited',
      confidence: 'medium',
      url: 'https://partsbase.com/gen-hyd-737-b',
      warnings: ['Not OEM certified', 'Verify compatibility before installation'],
    }
  ]

  // Handle file upload
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (!files) return

    const newAttachments: FileAttachment[] = Array.from(files).map(file => ({
      id: Math.random().toString(36).substr(2, 9),
      name: file.name,
      type: file.type,
      size: file.size,
      url: URL.createObjectURL(file)
    }))

    setAttachments(prev => [...prev, ...newAttachments])

    toast({
      title: 'Files uploaded',
      description: `${files.length} file(s) added to your query`,
      status: 'success',
      duration: 3000,
      isClosable: true,
    })
  }

  // Remove attachment
  const removeAttachment = (id: string) => {
    setAttachments(prev => prev.filter(att => att.id !== id))
  }

  // Handle message send with real API integration
  const handleSendMessage = async () => {
    if (!inputValue.trim() && attachments.length === 0) return

    const userMessage: Message = {
      id: Math.random().toString(36).substr(2, 9),
      type: 'user',
      content: inputValue,
      timestamp: new Date(),
      attachments: attachments.length > 0 ? [...attachments] : undefined,
    }

    const query = inputValue.trim()
    setMessages(prev => [...prev, userMessage])
    setInputValue('')
    setAttachments([])
    setIsLoading(true)

    try {
      // Start research session
      const request = {
        query,
        priority: 'normal' as const,
        research_depth: 'quick' as const, // Use quick for search-like responses
        context_data: attachments.length > 0 ? { attachments: attachments.map(a => a.name) } : undefined,
      }

      let result
      if (token) {
        result = await researchAPI.startAuthenticatedResearchSession(token, request)
      } else {
        result = await researchAPI.startResearchSession(request)
      }

      setCurrentSessionId(result.session_id)

      // Add loading message
      const loadingMessage: Message = {
        id: Math.random().toString(36).substr(2, 9),
        type: 'assistant',
        content: 'Searching for relevant parts and information...',
        timestamp: new Date(),
        isLoading: true,
      }
      setMessages(prev => [...prev, loadingMessage])

      // Poll for results
      pollForResults(result.session_id, loadingMessage.id)

    } catch (error: any) {
      console.error('Search failed:', error)

      const errorMessage: Message = {
        id: Math.random().toString(36).substr(2, 9),
        type: 'assistant',
        content: `I'm sorry, I encountered an error while searching: ${error.message || 'Unknown error'}. Please try again.`,
        timestamp: new Date(),
      }

      setMessages(prev => [...prev, errorMessage])
      setIsLoading(false)

      toast({
        title: 'Search failed',
        description: error.message || 'Could not perform search',
        status: 'error',
        duration: 5000,
        isClosable: true,
      })
    }
  }

  // Poll for research results
  const pollForResults = async (sessionId: string, loadingMessageId: string) => {
    const maxPolls = 20 // Poll for up to 3.3 minutes (20 * 10 seconds)
    let pollCount = 0

    const poll = async () => {
      try {
        let session
        if (token) {
          session = await researchAPI.getAuthenticatedSessionStatus(token, sessionId)
        } else {
          session = await researchAPI.getSessionStatus(sessionId)
        }

        if (session.status === 'completed') {
          // Remove loading message and add results
          setMessages(prev => prev.filter(msg => msg.id !== loadingMessageId))

          const results = session.results || []
          const searchResults = convertResearchResultsToSearchResults(results)

          const assistantMessage: Message = {
            id: Math.random().toString(36).substr(2, 9),
            type: 'assistant',
            content: searchResults.length > 0
              ? `I found ${searchResults.length} relevant results for your search. Here are the most relevant options based on your requirements:`
              : 'I completed the search but didn\'t find any specific results. Here\'s what I found:',
            timestamp: new Date(),
            searchResults: searchResults.length > 0 ? searchResults : undefined,
          }

          // If no search results, add the raw research content
          if (searchResults.length === 0 && results.length > 0) {
            assistantMessage.content += '\n\n' + results.map(r => r.content).join('\n\n')
          }

          setMessages(prev => [...prev, assistantMessage])
          setIsLoading(false)
          return
        }

        if (session.status === 'failed') {
          setMessages(prev => prev.filter(msg => msg.id !== loadingMessageId))

          const errorMessage: Message = {
            id: Math.random().toString(36).substr(2, 9),
            type: 'assistant',
            content: 'I encountered an error while searching. Please try again with a different query.',
            timestamp: new Date(),
          }

          setMessages(prev => [...prev, errorMessage])
          setIsLoading(false)
          return
        }

        // Continue polling if still running
        if (session.status === 'running' || session.status === 'pending') {
          pollCount++
          if (pollCount < maxPolls) {
            setTimeout(poll, 10000) // Poll every 10 seconds
          } else {
            // Timeout
            setMessages(prev => prev.filter(msg => msg.id !== loadingMessageId))

            const timeoutMessage: Message = {
              id: Math.random().toString(36).substr(2, 9),
              type: 'assistant',
              content: 'The search is taking longer than expected. Please try again or check the Research page for results.',
              timestamp: new Date(),
            }

            setMessages(prev => [...prev, timeoutMessage])
            setIsLoading(false)
          }
        }
      } catch (error) {
        console.error('Failed to poll session status:', error)
        setMessages(prev => prev.filter(msg => msg.id !== loadingMessageId))
        setIsLoading(false)
      }
    }

    // Start polling after a short delay
    setTimeout(poll, 3000)
  }

  // Convert research results to search results format
  const convertResearchResultsToSearchResults = (results: any[]): SearchResult[] => {
    // For now, return sample results since the mock API returns generic content
    // In a real implementation, this would parse the actual research results
    if (results.length > 0) {
      return sampleResults.slice(0, Math.min(3, sampleResults.length)) // Return a subset
    }
    return []
  }

  // Handle export
  const handleExport = (type: 'pdf' | 'excel' | 'csv') => {
    toast({
      title: `Exporting as ${type.toUpperCase()}`,
      description: 'Your file will be ready for download shortly',
      status: 'info',
      duration: 3000,
      isClosable: true,
    })

    // Simulate file generation
    setTimeout(() => {
      toast({
        title: 'Export ready',
        description: `Your ${type.toUpperCase()} file has been generated`,
        status: 'success',
        duration: 5000,
        isClosable: true,
      })
    }, 3000)
  }

  // Handle order placement
  const handlePlaceOrder = (result: SearchResult) => {
    toast({
      title: 'Order initiated',
      description: `Starting order process for ${result.partNumber}`,
      status: 'info',
      duration: 3000,
      isClosable: true,
    })
  }

  // Copy result to clipboard
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast({
      title: 'Copied to clipboard',
      status: 'success',
      duration: 2000,
      isClosable: true,
    })
  }

  return (
    <Box minH="100vh" bg="gray.900">
      {/* Header with K.A.R.E.N. Logo */}
      <Header
        title="Procurement Search"
        subtitle="Navigate and execute intelligent procurement decisions"
      >
        <Menu>
          <MenuButton as={Button} variant="ghost" size="sm">
            Export Results
          </MenuButton>
          <MenuList bg="gray.800" borderColor="gray.700">
            {exportOptions.map((option) => (
              <MenuItem
                key={option.type}
                icon={option.icon}
                onClick={() => handleExport(option.type)}
                bg="gray.800"
                _hover={{ bg: 'gray.700' }}
                color="gray.100"
              >
                {option.label}
              </MenuItem>
            ))}
          </MenuList>
        </Menu>
      </Header>

      {/* Main Content */}
      <Container maxW="7xl" h="calc(100vh - 80px)">
        <Flex h="full">
          {/* Chat Interface */}
          <Box flex="1" display="flex" flexDirection="column" p={6}>
            {/* Messages Area */}
            <Box
              flex="1"
              overflowY="auto"
              mb={4}
              css={{
                '&::-webkit-scrollbar': {
                  width: '4px',
                },
                '&::-webkit-scrollbar-track': {
                  width: '6px',
                },
                '&::-webkit-scrollbar-thumb': {
                  background: 'var(--chakra-colors-gray-600)',
                  borderRadius: '24px',
                },
              }}
            >
              <VStack spacing={6} align="stretch">
                {messages.map((message) => (
                  <MessageBubble
                    key={message.id}
                    message={message}
                    onResultClick={setSelectedResult}
                    onPreviewOpen={onPreviewOpen}
                    onPlaceOrder={handlePlaceOrder}
                    onCopyToClipboard={copyToClipboard}
                  />
                ))}
                {isLoading && (
                  <Box display="flex" justifyContent="flex-start">
                    <Card bg="gray.800" borderColor="gray.700" maxW="md">
                      <CardBody>
                        <HStack spacing={3}>
                          <Spinner size="sm" color="brand.500" />
                          <Text color="gray.300">Searching...</Text>
                        </HStack>
                      </CardBody>
                    </Card>
                  </Box>
                )}
                <div ref={messagesEndRef} />
              </VStack>
            </Box>

            {/* Input Area */}
            <Card bg="gray.800" borderColor="gray.700">
              <CardBody>
                {/* Attachments Preview */}
                {attachments.length > 0 && (
                  <Box mb={4}>
                    <Text color="gray.400" fontSize="sm" mb={2}>
                      Attached files:
                    </Text>
                    <HStack spacing={2} flexWrap="wrap">
                      {attachments.map((attachment) => (
                        <Badge
                          key={attachment.id}
                          colorScheme="brand"
                          variant="subtle"
                          p={2}
                          borderRadius="md"
                          display="flex"
                          alignItems="center"
                          gap={2}
                        >
                          {attachment.type.startsWith('image/') ? (
                            <FiImage size={14} />
                          ) : (
                            <FiFileText size={14} />
                          )}
                          <Text fontSize="xs">{attachment.name}</Text>
                          <IconButton
                            size="xs"
                            variant="ghost"
                            icon={<FiX />}
                            onClick={() => removeAttachment(attachment.id)}
                            aria-label="Remove attachment"
                          />
                        </Badge>
                      ))}
                    </HStack>
                  </Box>
                )}

                {/* Input Row */}
                <HStack spacing={3}>
                  <Input
                    ref={fileInputRef}
                    type="file"
                    multiple
                    accept=".pdf,.png,.jpg,.jpeg,.gif,.doc,.docx"
                    onChange={handleFileUpload}
                    display="none"
                  />

                  <Tooltip label="Attach files (PDF, images, documents)">
                    <IconButton
                      icon={<FiPaperclip />}
                      variant="ghost"
                      onClick={() => fileInputRef.current?.click()}
                      aria-label="Attach files"
                      color="gray.400"
                      _hover={{ color: 'gray.200', bg: 'gray.700' }}
                    />
                  </Tooltip>

                  <Textarea
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                    placeholder="Ask me to find parts, tools, or materials... You can also upload files for context."
                    resize="none"
                    minH="60px"
                    maxH="120px"
                    onKeyPress={(e) => {
                      if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault()
                        handleSendMessage()
                      }
                    }}
                  />

                  <Tooltip label="Send message">
                    <IconButton
                      icon={<FiSend />}
                      colorScheme="brand"
                      onClick={handleSendMessage}
                      isDisabled={!inputValue.trim() && attachments.length === 0}
                      aria-label="Send message"
                    />
                  </Tooltip>
                </HStack>

                {/* Quick Actions */}
                <HStack spacing={2} mt={3} flexWrap="wrap">
                  <Text color="gray.500" fontSize="xs">
                    Quick actions:
                  </Text>
                  {[
                    'Find Boeing 737 parts',
                    'Search hydraulic filters',
                    'Compare prices',
                    'FAA PMA alternatives'
                  ].map((action) => (
                    <Button
                      key={action}
                      size="xs"
                      variant="ghost"
                      color="gray.400"
                      _hover={{ color: 'gray.200', bg: 'gray.700' }}
                      onClick={() => setInputValue(action)}
                    >
                      {action}
                    </Button>
                  ))}
                </HStack>
              </CardBody>
            </Card>
          </Box>
        </Flex>
      </Container>

      {/* Preview Modal */}
      <Modal isOpen={isPreviewOpen} onClose={onPreviewClose} size="xl">
        <ModalOverlay />
        <ModalContent bg="gray.800" borderColor="gray.700">
          <ModalHeader color="gray.100">
            {selectedResult?.title || 'Result Preview'}
          </ModalHeader>
          <ModalCloseButton color="gray.400" />
          <ModalBody pb={6}>
            {selectedResult && (
              <VStack spacing={4} align="stretch">
                {selectedResult.image && (
                  <Image
                    src={selectedResult.image}
                    alt={selectedResult.title}
                    borderRadius="md"
                    maxH="200px"
                    objectFit="cover"
                  />
                )}

                <Box>
                  <Text color="gray.300" mb={2}>
                    {selectedResult.description}
                  </Text>

                  <SimpleGrid columns={2} spacing={4} mt={4}>
                    <Box>
                      <Text color="gray.400" fontSize="sm">Vendor</Text>
                      <Text color="gray.100">{selectedResult.vendor}</Text>
                    </Box>
                    <Box>
                      <Text color="gray.400" fontSize="sm">Part Number</Text>
                      <Text color="gray.100">{selectedResult.partNumber}</Text>
                    </Box>
                    <Box>
                      <Text color="gray.400" fontSize="sm">Price</Text>
                      <Text color="gray.100" fontWeight="bold">{selectedResult.price}</Text>
                    </Box>
                    <Box>
                      <Text color="gray.400" fontSize="sm">Availability</Text>
                      <Badge
                        colorScheme={
                          selectedResult.availability === 'in-stock' ? 'green' :
                          selectedResult.availability === 'limited' ? 'yellow' : 'red'
                        }
                      >
                        {selectedResult.availability}
                      </Badge>
                    </Box>
                  </SimpleGrid>

                  {selectedResult.specifications && (
                    <Box mt={4}>
                      <Text color="gray.400" fontSize="sm" mb={2}>Specifications</Text>
                      <SimpleGrid columns={2} spacing={2}>
                        {Object.entries(selectedResult.specifications).map(([key, value]) => (
                          <Box key={key}>
                            <Text color="gray.500" fontSize="xs">{key}</Text>
                            <Text color="gray.200" fontSize="sm">{value}</Text>
                          </Box>
                        ))}
                      </SimpleGrid>
                    </Box>
                  )}

                  {selectedResult.warnings && selectedResult.warnings.length > 0 && (
                    <Alert status="warning" bg="orange.900" borderColor="orange.700" mt={4}>
                      <AlertIcon />
                      <Box>
                        <AlertTitle fontSize="sm">Important Notes:</AlertTitle>
                        <AlertDescription fontSize="xs">
                          {selectedResult.warnings.join('. ')}
                        </AlertDescription>
                      </Box>
                    </Alert>
                  )}

                  <HStack spacing={3} mt={6}>
                    <Button
                      colorScheme="brand"
                      leftIcon={<FiShoppingCart />}
                      onClick={() => handlePlaceOrder(selectedResult)}
                    >
                      Place Order
                    </Button>
                    <Button
                      variant="outline"
                      leftIcon={<FiExternalLink />}
                      onClick={() => window.open(selectedResult.url, '_blank')}
                    >
                      View on Site
                    </Button>
                    <Button
                      variant="ghost"
                      leftIcon={<FiCopy />}
                      onClick={() => copyToClipboard(selectedResult.partNumber || '')}
                    >
                      Copy Part #
                    </Button>
                  </HStack>
                </Box>
              </VStack>
            )}
          </ModalBody>
        </ModalContent>
      </Modal>
    </Box>
  )
}

export default SearchPage
