"""
Specialized agent definitions for Expendra.
"""
from typing import List, Dict, Any
from crewai import Agent
from langchain_core.language_models import BaseLanguageModel
from langchain_core.tools import BaseTool

class AgentFactory:
    """
    Factory class for creating specialized agents with predefined roles and capabilities.
    """
    
    @staticmethod
    def create_sourcing_specialist(llm: BaseLanguageModel, tools: List[BaseTool]) -> Agent:
        """
        Create a Sourcing Specialist agent focused on finding parts and suppliers.
        
        Args:
            llm: Language model instance
            tools: Available tools for the agent
            
        Returns:
            Configured Sourcing Specialist agent
        """
        return Agent(
            role="Sourcing Specialist",
            goal="""Find and identify parts, tools, and materials from various online sources. 
            Locate exact part numbers, compatible alternatives, and FAA PMA approved substitutes. 
            Extract accurate pricing and availability information from supplier websites.""",
            
            backstory="""You are an expert sourcing specialist with 15+ years of experience in 
            aerospace and industrial procurement. You have deep knowledge of:
            
            - Aerospace part numbering systems (Boeing, Airbus, military specs)
            - FAA PMA (Parts Manufacturer Approval) regulations and alternatives
            - Industrial supplier networks (Grainger, McMaster-Carr, MSC Industrial)
            - Aviation suppliers (SkyGeek, Aircraft Spruce, PartsBase)
            - Cross-referencing part numbers across manufacturers
            - Understanding technical specifications and compatibility requirements
            
            You excel at navigating complex supplier websites, understanding technical catalogs,
            and finding the exact parts needed even when given incomplete information. You always
            verify part compatibility and provide multiple sourcing options when possible.""",
            
            verbose=True,
            allow_delegation=False,
            tools=tools,
            llm=llm,
            max_iter=5,
            memory=True,
            
            # Additional configuration for better performance
            step_callback=None,
            system_template="""You are a Sourcing Specialist. When given a part request:

1. IDENTIFY: Extract part numbers, specifications, and requirements
2. SEARCH: Use web tools to find the part on relevant supplier sites
3. VERIFY: Check compatibility and specifications
4. ALTERNATIVES: Look for compatible alternatives and PMA parts
5. PRICING: Gather pricing from multiple sources
6. DOCUMENT: Provide structured findings with sources

Always be thorough and provide multiple options when available."""
        )
    
    @staticmethod
    def create_research_analyst(llm: BaseLanguageModel, tools: List[BaseTool]) -> Agent:
        """
        Create a Research Analyst agent focused on market research and analysis.
        
        Args:
            llm: Language model instance
            tools: Available tools for the agent
            
        Returns:
            Configured Research Analyst agent
        """
        return Agent(
            role="Research Analyst",
            goal="""Conduct comprehensive market research and analysis on parts, suppliers, 
            and market trends. Analyze supplier capabilities, compare prices across vendors, 
            and identify market opportunities and risks.""",
            
            backstory="""You are a meticulous research analyst with a Master's degree in Supply 
            Chain Management and 12+ years of experience in aerospace and industrial markets. 
            Your expertise includes:
            
            - Market trend analysis and forecasting
            - Supplier capability assessment and risk analysis
            - Competitive pricing analysis and benchmarking
            - Supply chain disruption impact assessment
            - Regulatory compliance research (FAA, EASA, military standards)
            - Technology trend analysis affecting part availability
            - Geopolitical factors affecting supply chains
            
            You excel at gathering comprehensive market intelligence, analyzing complex data sets,
            and providing actionable insights that help procurement teams make informed decisions.
            You always consider multiple perspectives and provide balanced, well-researched analysis.""",
            
            verbose=True,
            allow_delegation=False,
            tools=tools,
            llm=llm,
            max_iter=5,
            memory=True,
            
            system_template="""You are a Research Analyst. When conducting market research:

1. ANALYZE: Review sourcing findings and identify research priorities
2. INVESTIGATE: Research market trends, supplier backgrounds, and industry factors
3. COMPARE: Analyze pricing, lead times, and supplier capabilities
4. ASSESS: Evaluate risks, opportunities, and market dynamics
5. SYNTHESIZE: Combine findings into comprehensive market intelligence
6. RECOMMEND: Provide strategic insights and recommendations

Focus on providing actionable intelligence that supports procurement decisions."""
        )
    
    @staticmethod
    def create_data_processor(llm: BaseLanguageModel, tools: List[BaseTool] = None) -> Agent:
        """
        Create a Data Processor agent focused on organizing and presenting findings.
        
        Args:
            llm: Language model instance
            tools: Available tools for the agent (optional, mainly uses analysis)
            
        Returns:
            Configured Data Processor agent
        """
        return Agent(
            role="Data Processor",
            goal="""Process, structure, and format research findings into clear, actionable 
            reports. Create structured comparisons, highlight key insights, and present 
            information in formats that support decision-making.""",
            
            backstory="""You are a data processing expert with a background in business 
            intelligence and technical writing. You have 10+ years of experience in:
            
            - Data analysis and visualization
            - Technical report writing and documentation
            - Business intelligence and dashboard creation
            - Procurement decision support systems
            - Risk assessment and scoring methodologies
            - Cost-benefit analysis and ROI calculations
            - Regulatory compliance documentation
            
            You excel at taking complex, unstructured research findings and transforming them
            into clear, actionable reports that busy procurement professionals can quickly
            understand and act upon. You always focus on the key decision points and provide
            clear recommendations with supporting rationale.""",
            
            verbose=True,
            allow_delegation=False,
            tools=tools or [],  # Data processor primarily analyzes rather than searches
            llm=llm,
            max_iter=3,
            memory=True,
            
            system_template="""You are a Data Processor. When processing research findings:

1. ORGANIZE: Structure all findings from sourcing and research phases
2. ANALYZE: Identify patterns, gaps, and key insights
3. COMPARE: Create clear comparisons between alternatives
4. PRIORITIZE: Rank options based on criteria (price, availability, quality)
5. SUMMARIZE: Create executive summary with key recommendations
6. FORMAT: Present in clear, actionable format

Always focus on what the buyer needs to know to make a decision."""
        )

class WorkflowTemplates:
    """
    Predefined workflow templates for common research scenarios.
    """
    
    @staticmethod
    def get_part_sourcing_workflow() -> Dict[str, Any]:
        """Get workflow template for part sourcing tasks."""
        return {
            "name": "Part Sourcing Workflow",
            "description": "Standard workflow for finding and sourcing specific parts",
            "phases": [
                {
                    "name": "Initial Sourcing",
                    "agent": "sourcing_specialist",
                    "focus": "part_identification",
                    "deliverables": ["part_specs", "initial_suppliers", "pricing_estimates"]
                },
                {
                    "name": "Market Research",
                    "agent": "research_analyst", 
                    "focus": "market_analysis",
                    "deliverables": ["supplier_analysis", "market_trends", "risk_assessment"]
                },
                {
                    "name": "Final Processing",
                    "agent": "data_processor",
                    "focus": "decision_support",
                    "deliverables": ["comparison_matrix", "recommendations", "final_report"]
                }
            ]
        }
    
    @staticmethod
    def get_market_research_workflow() -> Dict[str, Any]:
        """Get workflow template for deep market research tasks."""
        return {
            "name": "Market Research Workflow",
            "description": "Comprehensive workflow for market analysis and trend research",
            "phases": [
                {
                    "name": "Market Scanning",
                    "agent": "research_analyst",
                    "focus": "market_overview",
                    "deliverables": ["market_size", "key_players", "trend_analysis"]
                },
                {
                    "name": "Detailed Sourcing",
                    "agent": "sourcing_specialist",
                    "focus": "supplier_deep_dive",
                    "deliverables": ["supplier_profiles", "capability_matrix", "pricing_analysis"]
                },
                {
                    "name": "Strategic Analysis",
                    "agent": "data_processor",
                    "focus": "strategic_insights",
                    "deliverables": ["swot_analysis", "strategic_recommendations", "action_plan"]
                }
            ]
        }
    
    @staticmethod
    def get_alternative_search_workflow() -> Dict[str, Any]:
        """Get workflow template for finding part alternatives."""
        return {
            "name": "Alternative Search Workflow", 
            "description": "Specialized workflow for finding part alternatives and substitutes",
            "phases": [
                {
                    "name": "Alternative Identification",
                    "agent": "sourcing_specialist",
                    "focus": "alternative_parts",
                    "deliverables": ["alternative_list", "compatibility_matrix", "pma_options"]
                },
                {
                    "name": "Alternative Analysis",
                    "agent": "research_analyst",
                    "focus": "alternative_evaluation", 
                    "deliverables": ["performance_comparison", "cost_analysis", "risk_evaluation"]
                },
                {
                    "name": "Recommendation Synthesis",
                    "agent": "data_processor",
                    "focus": "alternative_ranking",
                    "deliverables": ["ranked_alternatives", "decision_matrix", "implementation_guide"]
                }
            ]
        }
