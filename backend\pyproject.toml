[tool.black]
line-length = 88
target-version = ['py310']
include = '\.pyi?$'
exclude = '''
/(
    \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
)/
'''

[tool.flake8]
max-line-length = 88
ignore = ["E203", "E501"] # E501 is line too long, E203 is whitespace before colon
exclude = [
    ".git",
    "__pycache__",
    "backend/venv",
    "backend/api/deps.py",
    "backend/tests/",
]