#!/usr/bin/env python3
"""
Database utility script for Expendra backend.
This script helps with database operations like checking users and resetting passwords.
"""
import sys
import os
from sqlalchemy.orm import Session
from passlib.context import CryptContext

# Add the backend directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import SessionLocal, engine
from models import User, UserSettings

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def get_password_hash(password: str) -> str:
    """Hash a password."""
    return pwd_context.hash(password)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against its hash."""
    return pwd_context.verify(plain_password, hashed_password)

def list_users():
    """List all users in the database."""
    db = SessionLocal()
    try:
        users = db.query(User).all()
        print(f"Found {len(users)} users in the database:")
        for user in users:
            print(f"  ID: {user.id}")
            print(f"  Email: {user.email}")
            print(f"  Username: {user.username}")
            print(f"  Full Name: {user.full_name}")
            print(f"  Active: {user.is_active}")
            print(f"  Verified: {user.is_verified}")
            print(f"  Created: {user.created_at}")
            print("  ---")
    finally:
        db.close()

def create_test_user():
    """Create or update the test user with known credentials."""
    db = SessionLocal()
    try:
        # Check if user exists
        user = db.query(User).filter(User.email == "<EMAIL>").first()
        
        if user:
            print("User <EMAIL> already exists. Updating password...")
            user.hashed_password = get_password_hash("testpassword123")
            user.is_active = True
            user.is_verified = True
            db.commit()
            print("Password updated successfully!")
        else:
            print("Creating new test user...")
            # Create new user
            hashed_password = get_password_hash("testpassword123")
            new_user = User(
                email="<EMAIL>",
                username="testuser",
                full_name="Test User",
                hashed_password=hashed_password,
                is_active=True,
                is_verified=True
            )
            
            db.add(new_user)
            db.commit()
            db.refresh(new_user)
            
            # Create default user settings
            default_settings = UserSettings(
                user_id=new_user.id,
                default_llm_provider="openai",
                default_model="gpt-3.5-turbo",
                theme="dark",
                primary_color="blue",
                font_size="medium",
                animations_enabled=True,
                default_research_depth="standard",
                auto_export_format="pdf"
            )
            
            db.add(default_settings)
            db.commit()
            print("Test user created successfully!")
            
    except Exception as e:
        print(f"Error: {e}")
        db.rollback()
    finally:
        db.close()

def verify_test_user():
    """Verify that the test user can authenticate."""
    db = SessionLocal()
    try:
        user = db.query(User).filter(User.email == "<EMAIL>").first()
        if user:
            if verify_password("testpassword123", user.hashed_password):
                print("✅ Test user authentication verified!")
                print(f"User: {user.email} ({user.username})")
                print(f"Active: {user.is_active}")
                print(f"Verified: {user.is_verified}")
            else:
                print("❌ Password verification failed!")
        else:
            print("❌ Test user not found!")
    finally:
        db.close()

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python db_utils.py [list|create|verify]")
        print("  list   - List all users")
        print("  create - Create/update test user")
        print("  verify - Verify test user credentials")
        sys.exit(1)
    
    command = sys.argv[1]
    
    if command == "list":
        list_users()
    elif command == "create":
        create_test_user()
    elif command == "verify":
        verify_test_user()
    else:
        print(f"Unknown command: {command}")
        sys.exit(1)
