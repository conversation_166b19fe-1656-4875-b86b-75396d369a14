/** @type {import('eslint').Linter.Config} */
module.exports = {
  root: true,
  env: { browser: true, es2021: true, node: true },
  extends: [
    'eslint:recommended',
    'plugin:@typescript-eslint/recommended',
    'plugin:react-hooks/recommended',
    'prettier', // Integrates Prettier rules
  ],
  ignorePatterns: ['node_modules', 'dist', 'build', '.eslintrc\.cjs'],
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module',
    project: ['./tsconfig.json', './tsconfig.node.json'], // For ESLint to parse TS correctly
    tsconfigRootDir: __dirname,
  },
  plugins: ['react', 'react-hooks', '@typescript-eslint'],
  rules: {
    'react/prop-types': 'off', // PropTypes are not needed with TypeScript
    'react-refresh/only-export-components': ['warn', { allowConstantExport: true }],
    // Add any custom rules here if needed, e.g.
    // '@typescript-eslint/no-explicit-any': 'off',
  },
};