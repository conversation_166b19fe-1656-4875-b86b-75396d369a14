# Expendra

Expendra is an AI-powered purchasing assistant designed for aerospace buyers, with capabilities extending to non-aviation items and materials. It focuses on sourcing, pricing, alternate part number search, FAA PMA alternatives, and deep market research. The system features a modern, intuitive user interface inspired by OpenAI's design, robust backend services, multi-agent AI capabilities orchestrated by CrewAI, and scalable deployment on AWS.

## Key Features

*   **AI-Powered Sourcing & Pricing:** Automates the process of finding, comparing, and pricing parts and materials.
*   **Deep Market Research:** Conducts in-depth research on parts, tools, chemicals, and other items.
*   **Multi-Agent System:** Utilizes CrewAI to orchestrate specialized AI agents for various tasks.
*   **Web Interaction:** Interacts with supplier portals, e-commerce sites, and search engines using Playwright.
*   **Data Export:** Generates findings in PDF and Excel formats.
*   **Customizable LLM Integration:** Supports multiple LLM providers (OpenAI, Gemini, Anthropic, Ollama, etc.).
*   **Modern UI/UX:** Features a clean interface with dark mode, animations, and intuitive navigation, inspired by OpenAI's design.
*   **Persistent Memory:** Stores research findings and user configurations in a PostgreSQL database.
*   **Scalable AWS Deployment:** Built with cloud deployment in mind, utilizing services like ECS, RDS, S3, and CloudFront.

## Tech Stack

*   **Frontend:** React with TypeScript, Chakra UI
*   **Backend:** Python with FastAPI
*   **AI Orchestration:** CrewAI, LangChain
*   **Web Scraping:** Playwright
*   **Database:** PostgreSQL with SQLAlchemy
*   **CI/CD:** GitHub Actions, AWS Services (ECS, RDS, S3, CloudFront)

## Getting Started

### Prerequisites

*   Node.js (v18+)
*   Python (v3.10+)
*   Docker (for containerization and potentially local development)
*   PostgreSQL (for local development database)

### Local Development Setup

1.  **Clone the Repository:**
    ```bash
    git clone https://github.com/PapaBear1981/Expendra
    cd Expendra
    ```
2.  **Backend Setup:**
    *   Create a virtual environment: `python -m venv venv`
    *   Activate the environment: `source venv/bin/activate` (or `venv\Scripts\activate` on Windows)
    *   Install dependencies: `pip install -r backend/requirements.txt`
    *   Configure environment variables (e.g., for API keys, database connection) in a `.env` file in the `backend/` directory.
    *   Run the FastAPI application: `uvicorn backend.main:app --reload`
3.  **Frontend Setup:**
    *   Navigate to the frontend directory: `cd frontend`
    *   Install dependencies: `npm install` or `yarn install`
    *   Start the development server: `npm start` or `yarn start`
4.  **Database Setup:**
    *   Ensure PostgreSQL is running.
    *   Create a database for Expendra.
    *   Run database migrations (details to be added once ORM is set up).

## Contributing

Please refer to the `rules.md` file for detailed contribution guidelines, coding standards, and workflow practices.

## License

This project is licensed under the MIT License - see the `LICENSE` file for details.