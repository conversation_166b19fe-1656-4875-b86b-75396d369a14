import {
  Box,
  Container,
  Heading,
  Text,
  VStack,
  HStack,
  Button,
  Card,
  CardBody,
  Grid,
  GridItem,
  Badge,
  Avatar,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  IconButton,
  SimpleGrid,
  Flex,
  Progress,
  useColorModeValue,
  Spinner,
  Alert,
  AlertIcon,
} from '@chakra-ui/react'
import {
  FaChevronDown,
  FaCog,
  FaSearch,
  FaFileAlt,
  FaRocket,
  FaChartLine,
  FaDatabase,
  FaClock,
  FaUsers,
  FaShoppingCart,
  FaBell,
  FaCalendarAlt,
} from 'react-icons/fa'
import { useAuth } from '../contexts/AuthContext'
import { useNavigate } from 'react-router-dom'
import { useEffect, useState } from 'react'
import { CircularProgress, MetricCard, MiniChart, ActivityFeed, ProgressRing, DataTable, NotificationCenter } from '../components/dashboard'
import { Header } from '../components'
import { dashboardAPI } from '../services/api'

const DashboardPage = () => {
  const { user, logout, token } = useAuth()
  const navigate = useNavigate()
  const bgColor = useColorModeValue('gray.50', 'gray.900')
  const cardBg = useColorModeValue('white', 'gray.800')

  // State for dashboard data
  const [metricsData, setMetricsData] = useState<any>(null)
  const [activityData, setActivityData] = useState<any[]>([])
  const [chartData, setChartData] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Helper function to get time-based greeting
  const getGreeting = () => {
    const hour = new Date().getHours()
    if (hour < 12) return 'Good Morning'
    if (hour < 17) return 'Good Afternoon'
    return 'Good Evening'
  }

  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      if (!token) return

      try {
        setLoading(true)
        setError(null)

        // Fetch all dashboard data in parallel
        const [metrics, activity, chart] = await Promise.all([
          dashboardAPI.getMetrics(token),
          dashboardAPI.getActivity(token, 10),
          dashboardAPI.getChartData(token, 30)
        ])

        setMetricsData(metrics)
        setActivityData(activity)
        setChartData(chart)
      } catch (err: any) {
        console.error('Error fetching dashboard data:', err)
        setError(err.message || 'Failed to load dashboard data')

        // Fallback to mock data if API fails
        setMetricsData({
          totalSearches: { value: 0, trend: { value: 0, isPositive: true, label: 'vs last month' } },
          activeResearch: { value: 0, progress: { value: 0, max: 100, color: '#8B5CF6' } },
          reportsGenerated: { value: 0, trend: { value: 0, isPositive: true, label: 'this week' } },
          avgResponseTime: { value: 'N/A', trend: { value: 0, isPositive: true, label: 'improvement' } },
        })
        setActivityData([])
        setChartData([])
      } finally {
        setLoading(false)
      }
    }

    fetchDashboardData()
  }, [token])

  // Mock data sources for the table (this could also come from API)
  const detailedMetrics = [
    { source: 'SkyGeek', status: true, requests: metricsData?.totalSearches?.value || 0, successRate: 98.5, avgTime: '1.2s' },
    { source: 'Grainger', status: true, requests: Math.floor((metricsData?.totalSearches?.value || 0) * 0.7), successRate: 97.8, avgTime: '1.8s' },
    { source: 'PartsBase', status: true, requests: Math.floor((metricsData?.totalSearches?.value || 0) * 0.5), successRate: 96.2, avgTime: '2.1s' },
    { source: 'McMaster-Carr', status: true, requests: Math.floor((metricsData?.totalSearches?.value || 0) * 0.3), successRate: 99.1, avgTime: '0.9s' },
    { source: 'Aircraft Spruce', status: false, requests: 0, successRate: 0, avgTime: 'N/A' },
  ]

  const tableColumns = [
    { key: 'source', label: 'Data Source', width: '30%' },
    { key: 'status', label: 'Status', width: '15%', align: 'center' as const },
    { key: 'requests', label: 'Requests', width: '20%', align: 'right' as const },
    { key: 'successRate', label: 'Success Rate', width: '20%', align: 'right' as const },
    { key: 'avgTime', label: 'Avg Time', width: '15%', align: 'right' as const },
  ]

  // Sample notification data
  const notificationData = [
    {
      id: '1',
      type: 'success' as const,
      title: 'Search Completed',
      message: 'Found 15 alternatives for hydraulic pump',
      timestamp: '2 min ago',
      read: false,
    },
    {
      id: '2',
      type: 'info' as const,
      title: 'Research Update',
      message: 'Market analysis is 75% complete',
      timestamp: '15 min ago',
      read: false,
    },
    {
      id: '3',
      type: 'warning' as const,
      title: 'Data Source Issue',
      message: 'Aircraft Spruce connection timeout',
      timestamp: '1 hour ago',
      read: true,
    },
  ]

  return (
    <Box minH="100vh" bg="gray.900">
      {/* Header with K.A.R.E.N. Logo */}
      <Header>
        <NotificationCenter
          notifications={notificationData}
          onMarkAsRead={(id) => console.log('Mark as read:', id)}
          onMarkAllAsRead={() => console.log('Mark all as read')}
          onDismiss={(id) => console.log('Dismiss:', id)}
        />
      </Header>

      {/* Main Content */}
      <Container maxW="7xl" py={8}>
        <VStack spacing={8} align="stretch">
          {/* Enhanced Welcome Section */}
          <Flex
            justify="space-between"
            align={{ base: 'start', md: 'center' }}
            direction={{ base: 'column', md: 'row' }}
            wrap="wrap"
            gap={4}
          >
            <VStack align="start" spacing={2}>
              <HStack spacing={2} wrap="wrap">
                <Text fontSize={{ base: 'xl', md: '2xl' }} color="gray.400">
                  {getGreeting()}!
                </Text>
                <Text fontSize={{ base: 'xl', md: '2xl' }} fontWeight="bold" color="gray.100">
                  {user?.full_name || user?.username}
                </Text>
              </HStack>
              <Text
                color="gray.400"
                fontSize={{ base: 'md', md: 'lg' }}
                maxW={{ base: 'full', md: '600px' }}
              >
                Let's navigate your procurement activities and execute efficient purchasing decisions
              </Text>
            </VStack>

            <HStack spacing={3} flexWrap="wrap">
              <Button
                leftIcon={<FaRocket />}
                colorScheme="brand"
                variant="gradient"
                size={{ base: 'md', md: 'lg' }}
                onClick={() => navigate('/search')}
                boxShadow="0 4px 15px rgba(0, 102, 204, 0.3)"
                w={{ base: 'full', sm: 'auto' }}
              >
                New Search
              </Button>
              <Button
                leftIcon={<FaCalendarAlt />}
                variant="outline"
                colorScheme="gray"
                size={{ base: 'md', md: 'lg' }}
                w={{ base: 'full', sm: 'auto' }}
                display={{ base: 'none', sm: 'flex' }}
              >
                Schedule
              </Button>
            </HStack>
          </Flex>

          {/* Loading State */}
          {loading && (
            <Flex justify="center" align="center" py={20}>
              <VStack spacing={4}>
                <Spinner size="xl" color="blue.500" />
                <Text color="gray.400">Loading dashboard data...</Text>
              </VStack>
            </Flex>
          )}

          {/* Error State */}
          {error && !loading && (
            <Alert status="error" bg="red.900" borderColor="red.700" borderWidth={1}>
              <AlertIcon />
              <VStack align="start" spacing={1}>
                <Text fontWeight="semibold">Failed to load dashboard data</Text>
                <Text fontSize="sm">{error}</Text>
              </VStack>
            </Alert>
          )}

          {/* Key Metrics Dashboard */}
          {!loading && metricsData && (
            <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6}>
            <MetricCard
              title="Total Searches"
              value={metricsData.totalSearches.value.toLocaleString()}
              subtitle="This month"
              icon={FaSearch}
              iconColor="blue"
              trend={metricsData.totalSearches.trend}
              variant="elevated"
            />

            <MetricCard
              title="Active Research"
              value={metricsData.activeResearch.value}
              subtitle="In progress"
              icon={FaChartLine}
              iconColor="purple"
              progress={metricsData.activeResearch.progress}
              variant="gradient"
            />

            <MetricCard
              title="Reports Generated"
              value={metricsData.reportsGenerated.value}
              subtitle="This week"
              icon={FaFileAlt}
              iconColor="green"
              trend={metricsData.reportsGenerated.trend}
              variant="elevated"
            />

            <MetricCard
              title="Avg Response Time"
              value={metricsData.avgResponseTime.value}
              subtitle="System performance"
              icon={FaClock}
              iconColor="orange"
              trend={metricsData.avgResponseTime.trend}
              variant="gradient"
            />
          </SimpleGrid>
          )}

          {/* Dashboard Grid */}
          {!loading && (
          <Grid
            templateColumns={{ base: '1fr', lg: '2fr 1fr' }}
            gap={{ base: 6, md: 8 }}
          >
            {/* Left Column - Charts and Analytics */}
            <VStack spacing={6} align="stretch">
              {/* Search Activity Chart */}
              <Card variant="elevated">
                <CardBody p={6}>
                  <VStack align="stretch" spacing={4}>
                    <HStack justify="space-between" align="center">
                      <VStack align="start" spacing={1}>
                        <Heading size="md" color="gray.100">
                          Search Activity
                        </Heading>
                        <Text fontSize="sm" color="gray.400">
                          Last 12 months trend
                        </Text>
                      </VStack>
                      <Badge colorScheme="green" variant="subtle" borderRadius="full">
                        +12.5% vs last period
                      </Badge>
                    </HStack>

                    <Box h="200px" display="flex" alignItems="end" justifyContent="center">
                      <MiniChart
                        data={chartData}
                        width={400}
                        height={150}
                        color="#3B82F6"
                        type="area"
                        fill={true}
                        animate={true}
                      />
                    </Box>
                  </VStack>
                </CardBody>
              </Card>

              {/* Quick Actions */}
              <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
                <Card variant="gradient" cursor="pointer" onClick={() => navigate('/search')}>
                  <CardBody p={6}>
                    <HStack spacing={4}>
                      <Box
                        p={3}
                        borderRadius="lg"
                        bg="blue.500"
                        color="white"
                      >
                        <FaSearch size={20} />
                      </Box>
                      <VStack align="start" spacing={1}>
                        <Text fontWeight="semibold" color="gray.100">
                          Start New Search
                        </Text>
                        <Text fontSize="sm" color="gray.400">
                          Find parts & materials
                        </Text>
                      </VStack>
                    </HStack>
                  </CardBody>
                </Card>

                <Card variant="gradient" cursor="pointer" onClick={() => navigate('/research')}>
                  <CardBody p={6}>
                    <HStack spacing={4}>
                      <Box
                        p={3}
                        borderRadius="lg"
                        bg="purple.500"
                        color="white"
                      >
                        <FaChartLine size={20} />
                      </Box>
                      <VStack align="start" spacing={1}>
                        <Text fontWeight="semibold" color="gray.100">
                          Deep Research
                        </Text>
                        <Text fontSize="sm" color="gray.400">
                          Market analysis
                        </Text>
                      </VStack>
                    </HStack>
                  </CardBody>
                </Card>
              </SimpleGrid>
            </VStack>

            {/* Right Column - Activity Feed and Stats */}
            <VStack spacing={6} align="stretch">
              {/* System Status */}
              <Card variant="elevated">
                <CardBody p={6}>
                  <VStack align="stretch" spacing={4}>
                    <Heading size="md" color="gray.100">
                      System Status
                    </Heading>

                    <VStack spacing={3} align="stretch">
                      <HStack justify="space-between">
                        <Text fontSize="sm" color="gray.400">API Response</Text>
                        <HStack spacing={2}>
                          <Box w={2} h={2} borderRadius="full" bg="green.400" />
                          <Text fontSize="sm" color="green.400">Operational</Text>
                        </HStack>
                      </HStack>

                      <HStack justify="space-between">
                        <Text fontSize="sm" color="gray.400">Data Sources</Text>
                        <HStack spacing={2}>
                          <Box w={2} h={2} borderRadius="full" bg="green.400" />
                          <Text fontSize="sm" color="green.400">12/12 Active</Text>
                        </HStack>
                      </HStack>

                      <HStack justify="space-between">
                        <Text fontSize="sm" color="gray.400">AI Agents</Text>
                        <HStack spacing={2}>
                          <Box w={2} h={2} borderRadius="full" bg="blue.400" />
                          <Text fontSize="sm" color="blue.400">3 Running</Text>
                        </HStack>
                      </HStack>
                    </VStack>
                  </VStack>
                </CardBody>
              </Card>

              {/* Recent Activity */}
              <Card variant="elevated">
                <CardBody p={6}>
                  <VStack align="stretch" spacing={4}>
                    <HStack justify="space-between" align="center">
                      <Heading size="md" color="gray.100">
                        Recent Activity
                      </Heading>
                      <Button size="sm" variant="ghost" color="gray.400">
                        View All
                      </Button>
                    </HStack>

                    <ActivityFeed
                      activities={activityData}
                      maxItems={4}
                      compact={true}
                    />
                  </VStack>
                </CardBody>
              </Card>

              {/* Quick Stats */}
              <SimpleGrid columns={2} spacing={4}>
                <Card variant="gradient">
                  <CardBody p={4} textAlign="center">
                    <VStack spacing={2}>
                      <Text fontSize="2xl" fontWeight="bold" color="gray.100">
                        98.5%
                      </Text>
                      <Text fontSize="xs" color="gray.400">
                        Success Rate
                      </Text>
                    </VStack>
                  </CardBody>
                </Card>

                <Card variant="gradient">
                  <CardBody p={4} textAlign="center">
                    <VStack spacing={2}>
                      <Text fontSize="2xl" fontWeight="bold" color="gray.100">
                        $2.4M
                      </Text>
                      <Text fontSize="xs" color="gray.400">
                        Cost Savings
                      </Text>
                    </VStack>
                  </CardBody>
                </Card>
              </SimpleGrid>
            </VStack>
          </Grid>
          )}
        </VStack>
      </Container>
    </Box>
  )
}

export default DashboardPage
