#!/usr/bin/env python3
"""
Test script to verify frontend API integration
"""
import requests
import json

def test_frontend_backend_integration():
    """Test that frontend can communicate with backend"""
    print("🧪 Testing Frontend-Backend Integration\n")
    
    # Test 1: Check if backend is accessible
    print("1. Testing backend accessibility...")
    try:
        response = requests.get("http://localhost:8000/api/v1/auth/me", timeout=5)
        print(f"   Backend response status: {response.status_code}")
        if response.status_code in [401, 422]:  # Expected for unauthenticated request
            print("   ✅ Backend is accessible and responding correctly")
        else:
            print("   ❌ Unexpected backend response")
    except Exception as e:
        print(f"   ❌ Backend not accessible: {e}")
        return False
    
    # Test 2: Check if frontend is serving files
    print("\n2. Testing frontend accessibility...")
    try:
        response = requests.get("http://localhost:5173", timeout=5)
        print(f"   Frontend response status: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ Frontend is accessible and serving content")
        else:
            print("   ❌ Frontend not responding correctly")
    except Exception as e:
        print(f"   ❌ Frontend not accessible: {e}")
        return False
    
    # Test 3: Test CORS by simulating a frontend request
    print("\n3. Testing CORS configuration...")
    try:
        headers = {
            'Origin': 'http://localhost:5173',
            'Content-Type': 'application/json'
        }
        response = requests.options("http://localhost:8000/api/v1/auth/register", headers=headers, timeout=5)
        print(f"   CORS preflight response status: {response.status_code}")
        if response.status_code in [200, 204]:
            print("   ✅ CORS is configured correctly")
        else:
            print("   ❌ CORS configuration issue")
    except Exception as e:
        print(f"   ❌ CORS test failed: {e}")
    
    # Test 4: Test a complete registration flow
    print("\n4. Testing complete registration flow...")
    try:
        headers = {
            'Origin': 'http://localhost:5173',
            'Content-Type': 'application/json'
        }
        data = {
            "email": "<EMAIL>",
            "username": "frontenduser",
            "password": "testpass123",
            "full_name": "Frontend Test User"
        }
        response = requests.post("http://localhost:8000/api/v1/auth/register", json=data, headers=headers, timeout=5)
        print(f"   Registration response status: {response.status_code}")
        
        if response.status_code == 201:
            print("   ✅ Registration flow works correctly")
            
            # Test login with the new user
            login_data = {
                "email": "<EMAIL>",
                "password": "testpass123"
            }
            login_response = requests.post("http://localhost:8000/api/v1/auth/login", json=login_data, headers=headers, timeout=5)
            print(f"   Login response status: {login_response.status_code}")
            
            if login_response.status_code == 200:
                print("   ✅ Login flow works correctly")
                
                # Test accessing protected route
                token = login_response.json()["access_token"]
                auth_headers = {
                    'Origin': 'http://localhost:5173',
                    'Authorization': f'Bearer {token}'
                }
                me_response = requests.get("http://localhost:8000/api/v1/auth/me", headers=auth_headers, timeout=5)
                print(f"   Protected route response status: {me_response.status_code}")
                
                if me_response.status_code == 200:
                    print("   ✅ Protected route access works correctly")
                    return True
                else:
                    print("   ❌ Protected route access failed")
            else:
                print("   ❌ Login flow failed")
        elif response.status_code == 400:
            print("   ⚠️  User might already exist, testing login instead...")
            login_data = {
                "email": "<EMAIL>",
                "password": "testpass123"
            }
            login_response = requests.post("http://localhost:8000/api/v1/auth/login", json=login_data, headers=headers, timeout=5)
            if login_response.status_code == 200:
                print("   ✅ Login flow works correctly")
                return True
        else:
            print("   ❌ Registration flow failed")
    except Exception as e:
        print(f"   ❌ Registration flow test failed: {e}")
    
    return False

def test_settings_api():
    """Test settings API endpoints"""
    print("\n🧪 Testing Settings API Integration\n")
    
    # First login to get a token
    try:
        login_data = {
            "email": "<EMAIL>",
            "password": "testpass123"
        }
        headers = {'Origin': 'http://localhost:5173', 'Content-Type': 'application/json'}
        login_response = requests.post("http://localhost:8000/api/v1/auth/login", json=login_data, headers=headers, timeout=5)
        
        if login_response.status_code != 200:
            print("❌ Cannot test settings - login failed")
            return False
            
        token = login_response.json()["access_token"]
        auth_headers = {
            'Origin': 'http://localhost:5173',
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
        
        # Test getting settings
        print("1. Testing get settings...")
        settings_response = requests.get("http://localhost:8000/api/v1/settings", headers=auth_headers, timeout=5)
        print(f"   Get settings status: {settings_response.status_code}")
        
        if settings_response.status_code == 200:
            print("   ✅ Get settings works correctly")
            
            # Test updating settings
            print("\n2. Testing update settings...")
            update_data = {
                "theme": "light",
                "default_llm_provider": "anthropic",
                "font_size": "large"
            }
            update_response = requests.put("http://localhost:8000/api/v1/settings", json=update_data, headers=auth_headers, timeout=5)
            print(f"   Update settings status: {update_response.status_code}")
            
            if update_response.status_code == 200:
                print("   ✅ Update settings works correctly")
                return True
            else:
                print("   ❌ Update settings failed")
        else:
            print("   ❌ Get settings failed")
            
    except Exception as e:
        print(f"❌ Settings API test failed: {e}")
    
    return False

def main():
    """Run all integration tests"""
    print("🚀 Starting Frontend-Backend Integration Tests\n")
    
    # Test basic integration
    integration_success = test_frontend_backend_integration()
    
    # Test settings API
    settings_success = test_settings_api()
    
    print(f"\n🏁 Integration Tests Complete!")
    print(f"   Frontend-Backend Integration: {'✅ PASS' if integration_success else '❌ FAIL'}")
    print(f"   Settings API Integration: {'✅ PASS' if settings_success else '❌ FAIL'}")
    
    if integration_success and settings_success:
        print("\n🎉 All tests passed! The frontend and backend are properly integrated.")
    else:
        print("\n⚠️  Some tests failed. Check the output above for details.")

if __name__ == "__main__":
    main()
