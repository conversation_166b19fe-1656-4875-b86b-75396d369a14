"""
User settings routes for Expendra API.
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from database import get_db
from models import User, UserSettings
from schemas import (
    UserSettingsResponse, UserSettingsUpdate, MessageResponse
)
from auth import get_current_active_user
from services.llm_service import llm_service

router = APIRouter(prefix="/settings", tags=["User Settings"])

@router.get("/", response_model=UserSettingsResponse)
async def get_user_settings(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get current user's settings.
    """
    settings = db.query(UserSettings).filter(UserSettings.user_id == current_user.id).first()
    
    if not settings:
        # Create default settings if they don't exist
        settings = UserSettings(
            user_id=current_user.id,
            default_llm_provider="openai",
            default_model="gpt-3.5-turbo",
            theme="dark",
            primary_color="blue",
            font_size="medium",
            animations_enabled=True,
            default_research_depth="standard",
            auto_export_format="pdf"
        )
        db.add(settings)
        db.commit()
        db.refresh(settings)
    
    return settings

@router.put("/", response_model=UserSettingsResponse)
async def update_user_settings(
    settings_update: UserSettingsUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Update current user's settings.
    """
    settings = db.query(UserSettings).filter(UserSettings.user_id == current_user.id).first()
    
    if not settings:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User settings not found"
        )
    
    # Update only provided fields
    update_data = settings_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(settings, field, value)
    
    db.commit()
    db.refresh(settings)
    
    return settings

@router.post("/reset", response_model=MessageResponse)
async def reset_user_settings(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Reset user settings to default values.
    """
    settings = db.query(UserSettings).filter(UserSettings.user_id == current_user.id).first()
    
    if not settings:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User settings not found"
        )
    
    # Reset to default values
    settings.default_llm_provider = "openai"
    settings.openai_api_key = None
    settings.anthropic_api_key = None
    settings.google_api_key = None
    settings.ollama_endpoint = None
    settings.default_model = "gpt-3.5-turbo"
    settings.theme = "dark"
    settings.primary_color = "blue"
    settings.font_size = "medium"
    settings.animations_enabled = True
    settings.default_research_depth = "standard"
    settings.auto_export_format = "pdf"
    
    db.commit()
    
    return MessageResponse(
        message="Settings reset to default values",
        success=True
    )

@router.get("/available-models", response_model=dict)
async def get_available_models():
    """
    Get available models from Google Gemini API.
    """
    try:
        models_response = await llm_service.get_available_models()
        return models_response
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "models": {
                "gemini-2.0-flash-001": {"name": "Gemini 2.0 Flash", "context_length": 1000000},
                "gemini-1.5-pro": {"name": "Gemini 1.5 Pro", "context_length": 2000000},
                "gemini-1.5-flash": {"name": "Gemini 1.5 Flash", "context_length": 1000000}
            }
        }

@router.get("/llm-providers", response_model=dict)
async def get_available_llm_providers():
    """
    Get list of available LLM providers and their supported models.
    For Google, fetches real models from API.
    """
    # Get real Google models
    google_models_response = await llm_service.get_available_models()
    google_models = []

    if google_models_response["status"] == "success":
        google_models = list(google_models_response["models"].keys())
    else:
        # Fallback models
        google_models = ["gemini-2.0-flash-001", "gemini-1.5-pro", "gemini-1.5-flash"]

    providers = {
        "openai": {
            "name": "OpenAI",
            "models": [
                "gpt-4",
                "gpt-4-turbo-preview",
                "gpt-3.5-turbo",
                "gpt-3.5-turbo-16k"
            ],
            "requires_api_key": True
        },
        "anthropic": {
            "name": "Anthropic",
            "models": [
                "claude-3-opus-20240229",
                "claude-3-sonnet-20240229",
                "claude-3-haiku-20240307",
                "claude-2.1",
                "claude-2.0"
            ],
            "requires_api_key": True
        },
        "google": {
            "name": "Google",
            "models": google_models,
            "requires_api_key": True
        },
        "ollama": {
            "name": "Ollama (Local)",
            "models": [
                "llama2",
                "codellama",
                "mistral",
                "neural-chat",
                "starling-lm"
            ],
            "requires_api_key": False,
            "requires_endpoint": True
        }
    }

    return {
        "providers": providers,
        "default_provider": "google",
        "default_model": "gemini-2.0-flash-001"
    }
