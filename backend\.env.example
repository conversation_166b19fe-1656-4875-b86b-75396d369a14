# Expendra Backend Environment Variables

# Database Configuration
DATABASE_URL=sqlite:///./expendra.db
# For PostgreSQL: DATABASE_URL=postgresql://username:password@localhost/expendra

# JWT Authentication
SECRET_KEY=your-secret-key-change-this-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30

# LLM Provider API Keys (Optional - users can configure these in settings)
OPENAI_API_KEY=your-openai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key
GOOGLE_API_KEY=your-google-api-key

# Ollama Configuration (for local LLM)
OLLAMA_ENDPOINT=http://localhost:11434

# Development Settings
DEBUG=true
LOG_LEVEL=info
