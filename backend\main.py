import os
from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from dotenv import load_dotenv

# Import database and models
from database import create_tables
from routes.auth import router as auth_router
# Temporarily commenting out routes with complex dependencies for testing
# from routes.settings import router as settings_router
# from routes.research import router as research_router
from routes.dashboard import router as dashboard_router

# Load environment variables from .env file
load_dotenv()

# Initialize FastAPI app
app = FastAPI(
    title="Expendra API",
    description="AI Purchasing Assistant Backend",
    version="0.1.0",
)

# Add CORS middleware for frontend integration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins for development
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# Add global OPTIONS handler for CORS preflight
@app.options("/{full_path:path}")
async def options_handler():
    """Handle CORS preflight requests for all routes."""
    return {"message": "OK"}

# Register API routes
app.include_router(auth_router, prefix="/api/v1")
# Temporarily commenting out routes with complex dependencies for testing
# app.include_router(settings_router, prefix="/api/v1")
# app.include_router(research_router, prefix="/api/v1")
app.include_router(dashboard_router, prefix="/api/v1")

# Database initialization
@app.on_event("startup")
async def startup_event():
    """
    Initialize database tables on startup.
    """
    create_tables()
    print("Database tables created successfully")
    print("Expendra backend started successfully!")

# --- API Endpoints ---

@app.get("/", summary="Root endpoint")
async def read_root():
    """
    Root endpoint for the API. Returns service status.
    """
    return {"message": "Welcome to the Expendra API!", "version": "0.1.0"}

@app.get("/health", summary="Health check endpoint")
async def health_check():
    """
    Health check endpoint to verify API service status.
    """
    return {"status": "healthy", "message": "Expendra API is running"}

# --- Add other API routes here ---
# Example:
# from myapp.api.v1.endpoints.auth import router as auth_router
# app.include_router(auth_router, prefix="/api/v1")

# --- Background Tasks and Setup ---
# For example, to initialize agents or connect to external services upon startup.
# This would be a place to set up CrewAI agents or connect to MCP services.
# @app.on_event("startup")
# async def startup_event():
#     # Initialize AI agents or other services here
#     # This could involve fetching configurations or libraries using MCP tools if needed.
#     print("Starting Expendra backend...")
#     pass

# @app.on_event("shutdown")
# async def shutdown_event():
#     print("Shutting down Expendra backend...")
#     pass

if __name__ == "__main__":
    import uvicorn
    # This block is for running the app directly during development
    # In production, you'd typically use a more robust deployment method (e.g., Gunicorn with Uvicorn workers, or Docker)
    print("Run this application with: uvicorn backend.main:app --reload")
    # uvicorn.run(app, host="0.0.0.0", port=8000) # Uncomment to run directly