"""
Authentication routes for Expendra API.
"""
from datetime import <PERSON><PERSON><PERSON>
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from database import get_db
from models import User, UserSettings
from schemas import (
    User<PERSON>reate, UserResponse, LoginRequest, TokenResponse, 
    MessageResponse, ErrorResponse
)
from auth import (
    get_password_hash, authenticate_user, create_access_token,
    get_current_active_user, get_user_by_email, get_user_by_username,
    ACCESS_TOKEN_EXPIRE_MINUTES
)

router = APIRouter(prefix="/auth", tags=["Authentication"])

@router.post("/register", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def register_user(user_data: UserCreate, db: Session = Depends(get_db)):
    """
    Register a new user account.
    """
    # Check if user already exists
    if get_user_by_email(db, user_data.email):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered"
        )
    
    if get_user_by_username(db, user_data.username):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Username already taken"
        )
    
    # Create new user
    hashed_password = get_password_hash(user_data.password)
    db_user = User(
        email=user_data.email,
        username=user_data.username,
        full_name=user_data.full_name,
        hashed_password=hashed_password,
        is_active=True,
        is_verified=False  # In production, you'd send a verification email
    )
    
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    
    # Create default user settings
    default_settings = UserSettings(
        user_id=db_user.id,
        default_llm_provider="openai",
        default_model="gpt-3.5-turbo",
        theme="dark",
        primary_color="blue",
        font_size="medium",
        animations_enabled=True,
        default_research_depth="standard",
        auto_export_format="pdf"
    )
    
    db.add(default_settings)
    db.commit()
    
    return db_user

@router.options("/login")
async def login_options():
    """Handle CORS preflight for login endpoint."""
    return {"message": "OK"}

@router.post("/login", response_model=TokenResponse)
async def login_user(login_data: LoginRequest, db: Session = Depends(get_db)):
    """
    Authenticate user and return access token.
    """
    user = authenticate_user(db, login_data.email, login_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user account"
        )
    
    # Create access token
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": str(user.id)}, expires_delta=access_token_expires
    )
    
    return TokenResponse(
        access_token=access_token,
        token_type="bearer",
        expires_in=ACCESS_TOKEN_EXPIRE_MINUTES * 60,  # Convert to seconds
        user=UserResponse.from_orm(user)
    )

@router.get("/me", response_model=UserResponse)
async def get_current_user_info(current_user: User = Depends(get_current_active_user)):
    """
    Get current authenticated user information.
    """
    return current_user

@router.post("/logout", response_model=MessageResponse)
async def logout_user(current_user: User = Depends(get_current_active_user)):
    """
    Logout user (client should discard the token).
    In a more sophisticated setup, you might maintain a token blacklist.
    """
    return MessageResponse(
        message="Successfully logged out",
        success=True
    )

@router.post("/refresh", response_model=TokenResponse)
async def refresh_token(current_user: User = Depends(get_current_active_user)):
    """
    Refresh the access token for the current user.
    """
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": str(current_user.id)}, expires_delta=access_token_expires
    )
    
    return TokenResponse(
        access_token=access_token,
        token_type="bearer",
        expires_in=ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        user=UserResponse.from_orm(current_user)
    )
