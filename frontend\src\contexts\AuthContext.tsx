import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { useToast } from '@chakra-ui/react'
import { authAPI } from '../services/api'

interface User {
  id: number
  email: string
  username: string
  full_name?: string
  is_active: boolean
  is_verified: boolean
  created_at: string
  updated_at: string
}

interface AuthContextType {
  user: User | null
  token: string | null
  isLoading: boolean
  login: (email: string, password: string) => Promise<boolean>
  register: (email: string, username: string, password: string, fullName?: string) => Promise<boolean>
  logout: () => void
  refreshToken: () => Promise<boolean>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

interface AuthProviderProps {
  children: ReactNode
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null)
  const [token, setToken] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const toast = useToast()

  // Initialize auth state from localStorage
  useEffect(() => {
    const initializeAuth = async () => {
      const storedToken = localStorage.getItem('expendra_token')
      if (storedToken) {
        setToken(storedToken)
        try {
          const userData = await authAPI.getCurrentUser(storedToken)
          setUser(userData)
        } catch (error) {
          console.error('Failed to get user data:', error)
          localStorage.removeItem('expendra_token')
          setToken(null)
        }
      }
      setIsLoading(false)
    }

    initializeAuth()
  }, [])

  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      setIsLoading(true)
      const response = await authAPI.login(email, password)
      
      setToken(response.access_token)
      setUser(response.user)
      localStorage.setItem('expendra_token', response.access_token)
      
      toast({
        title: 'Welcome back!',
        description: 'You have successfully logged in.',
        status: 'success',
        duration: 3000,
        isClosable: true,
      })
      
      return true
    } catch (error: any) {
      toast({
        title: 'Login failed',
        description: error.message || 'Invalid email or password',
        status: 'error',
        duration: 5000,
        isClosable: true,
      })
      return false
    } finally {
      setIsLoading(false)
    }
  }

  const register = async (
    email: string, 
    username: string, 
    password: string, 
    fullName?: string
  ): Promise<boolean> => {
    try {
      setIsLoading(true)
      await authAPI.register(email, username, password, fullName)
      
      toast({
        title: 'Registration successful!',
        description: 'Please log in with your new account.',
        status: 'success',
        duration: 5000,
        isClosable: true,
      })
      
      return true
    } catch (error: any) {
      toast({
        title: 'Registration failed',
        description: error.message || 'Failed to create account',
        status: 'error',
        duration: 5000,
        isClosable: true,
      })
      return false
    } finally {
      setIsLoading(false)
    }
  }

  const logout = () => {
    setUser(null)
    setToken(null)
    localStorage.removeItem('expendra_token')
    
    toast({
      title: 'Logged out',
      description: 'You have been successfully logged out.',
      status: 'info',
      duration: 3000,
      isClosable: true,
    })
  }

  const refreshToken = async (): Promise<boolean> => {
    if (!token) return false
    
    try {
      const response = await authAPI.refreshToken(token)
      setToken(response.access_token)
      setUser(response.user)
      localStorage.setItem('expendra_token', response.access_token)
      return true
    } catch (error) {
      console.error('Failed to refresh token:', error)
      logout()
      return false
    }
  }

  const value: AuthContextType = {
    user,
    token,
    isLoading,
    login,
    register,
    logout,
    refreshToken,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
