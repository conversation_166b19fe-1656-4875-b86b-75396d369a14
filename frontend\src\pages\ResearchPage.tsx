import React, { useState, useEffect } from 'react'
import {
  Box,
  Container,
  Heading,
  Text,
  VStack,
  HStack,
  Button,
  Card,
  CardBody,
  CardHeader,
  Input,
  Textarea,
  IconButton,
  Badge,
  Flex,
  Spacer,
  Divider,
  useToast,
  Progress,
  Spinner,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  SimpleGrid,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Select,
  FormControl,
  FormLabel,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
} from '@chakra-ui/react'
import {
  FiSend,
  FiRefreshCw,
  FiClock,
  FiCheckCircle,
  FiXCircle,
  FiAlertCircle,
  FiDownload,
  FiEye,
  FiTrash2,
} from 'react-icons/fi'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'
import { researchAPI } from '../services/api'
import { Header } from '../components'

interface ResearchSession {
  id: string
  query: string
  user_id: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  created_at: string
  results?: ResearchResult[]
}

interface ResearchResult {
  type: string
  content: string
  timestamp: string
  metadata?: Record<string, any>
}

const ResearchPage = () => {
  const navigate = useNavigate()
  const { user, token } = useAuth()
  const toast = useToast()
  const { isOpen, onOpen, onClose } = useDisclosure()

  // State management
  const [query, setQuery] = useState('')
  const [priority, setPriority] = useState<'low' | 'normal' | 'high' | 'urgent'>('normal')
  const [researchDepth, setResearchDepth] = useState<'quick' | 'standard' | 'deep'>('standard')
  const [isStarting, setIsStarting] = useState(false)
  const [sessions, setSessions] = useState<ResearchSession[]>([])
  const [isLoadingSessions, setIsLoadingSessions] = useState(false)
  const [connectionStatus, setConnectionStatus] = useState<'unknown' | 'connected' | 'error'>('unknown')
  const [selectedSession, setSelectedSession] = useState<ResearchSession | null>(null)

  // Test connection on component mount
  useEffect(() => {
    testConnection()
    if (token) {
      loadUserSessions()
    }
  }, [token])

  const testConnection = async () => {
    try {
      const result = await researchAPI.testConnection()
      setConnectionStatus(result.status === 'success' ? 'connected' : 'error')

      if (result.status === 'success') {
        toast({
          title: 'Research services connected',
          description: 'AI agents are ready for research tasks',
          status: 'success',
          duration: 3000,
          isClosable: true,
        })
      }
    } catch (error) {
      setConnectionStatus('error')
      console.error('Connection test failed:', error)
    }
  }

  const loadUserSessions = async () => {
    if (!token) return

    setIsLoadingSessions(true)
    try {
      const userSessions = await researchAPI.getUserSessions(token)
      setSessions(userSessions)
    } catch (error) {
      console.error('Failed to load sessions:', error)
      toast({
        title: 'Failed to load sessions',
        description: 'Could not retrieve your research sessions',
        status: 'error',
        duration: 5000,
        isClosable: true,
      })
    } finally {
      setIsLoadingSessions(false)
    }
  }

  const startResearchSession = async () => {
    if (!query.trim()) {
      toast({
        title: 'Query required',
        description: 'Please enter a research query',
        status: 'warning',
        duration: 3000,
        isClosable: true,
      })
      return
    }

    setIsStarting(true)
    try {
      const request = {
        query: query.trim(),
        priority,
        research_depth: researchDepth,
      }

      let result
      if (token) {
        // Use authenticated endpoint
        result = await researchAPI.startAuthenticatedResearchSession(token, request)
      } else {
        // Use public endpoint for testing
        result = await researchAPI.startResearchSession(request)
      }

      toast({
        title: 'Research session started',
        description: `Session ${result.session_id} is now running`,
        status: 'success',
        duration: 5000,
        isClosable: true,
      })

      // Clear form
      setQuery('')
      setPriority('normal')
      setResearchDepth('standard')

      // Reload sessions if authenticated
      if (token) {
        loadUserSessions()
      }

      // Poll for session status
      pollSessionStatus(result.session_id)

    } catch (error: any) {
      toast({
        title: 'Failed to start research',
        description: error.message || 'Could not start research session',
        status: 'error',
        duration: 5000,
        isClosable: true,
      })
    } finally {
      setIsStarting(false)
    }
  }

  const pollSessionStatus = async (sessionId: string) => {
    const maxPolls = 30 // Poll for up to 5 minutes (30 * 10 seconds)
    let pollCount = 0

    const poll = async () => {
      try {
        let session
        if (token) {
          session = await researchAPI.getAuthenticatedSessionStatus(token, sessionId)
        } else {
          session = await researchAPI.getSessionStatus(sessionId)
        }

        if (session.status === 'completed' || session.status === 'failed') {
          // Session finished, update UI
          if (token) {
            loadUserSessions()
          }

          toast({
            title: session.status === 'completed' ? 'Research completed' : 'Research failed',
            description: session.status === 'completed'
              ? `Found ${session.results?.length || 0} results`
              : 'Research session encountered an error',
            status: session.status === 'completed' ? 'success' : 'error',
            duration: 5000,
            isClosable: true,
          })
          return
        }

        // Continue polling if still running
        if (session.status === 'running' || session.status === 'pending') {
          pollCount++
          if (pollCount < maxPolls) {
            setTimeout(poll, 10000) // Poll every 10 seconds
          }
        }
      } catch (error) {
        console.error('Failed to poll session status:', error)
      }
    }

    // Start polling after a short delay
    setTimeout(poll, 2000)
  }
  const viewSessionDetails = (session: ResearchSession) => {
    setSelectedSession(session)
    onOpen()
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'green'
      case 'running': return 'blue'
      case 'pending': return 'yellow'
      case 'failed': return 'red'
      default: return 'gray'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <FiCheckCircle />
      case 'running': return <Spinner size="sm" />
      case 'pending': return <FiClock />
      case 'failed': return <FiXCircle />
      default: return <FiAlertCircle />
    }
  }

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString()
  }

  return (
    <Box minH="100vh" bg="gray.900">
      {/* Header with K.A.R.E.N. Logo */}
      <Header
        title="Deep Market Research"
        subtitle="Comprehensive research and analysis powered by AI agents"
      />

      <Box p={6}>
        <Container maxW="6xl">
          <VStack spacing={8}>
            {/* Connection Status */}
            <VStack spacing={4} textAlign="center">

            {/* Connection Status */}
            <Alert
              status={connectionStatus === 'connected' ? 'success' : connectionStatus === 'error' ? 'error' : 'info'}
              bg={connectionStatus === 'connected' ? 'green.900' : connectionStatus === 'error' ? 'red.900' : 'blue.900'}
              borderColor={connectionStatus === 'connected' ? 'green.600' : connectionStatus === 'error' ? 'red.600' : 'blue.600'}
              borderWidth="1px"
              borderRadius="md"
            >
              <AlertIcon />
              <AlertTitle>
                {connectionStatus === 'connected' ? 'Services Connected' :
                 connectionStatus === 'error' ? 'Connection Error' : 'Checking Connection...'}
              </AlertTitle>
              <AlertDescription>
                {connectionStatus === 'connected' ? 'AI research agents are ready' :
                 connectionStatus === 'error' ? 'Unable to connect to research services' : 'Testing research service connection...'}
              </AlertDescription>
              {connectionStatus === 'error' && (
                <Button size="sm" ml={4} onClick={testConnection} leftIcon={<FiRefreshCw />}>
                  Retry
                </Button>
              )}
            </Alert>
          </VStack>

          <Tabs variant="enclosed" colorScheme="brand" w="full">
            <TabList>
              <Tab color="gray.300" _selected={{ color: 'white', bg: 'brand.600' }}>
                New Research
              </Tab>
              <Tab color="gray.300" _selected={{ color: 'white', bg: 'brand.600' }}>
                Research Sessions ({sessions.length})
              </Tab>
            </TabList>

            <TabPanels>
              {/* New Research Tab */}
              <TabPanel>
                <Card bg="gray.800" borderColor="gray.700" w="full">
                  <CardHeader>
                    <Heading size="md" color="gray.100">
                      Start New Research Session
                    </Heading>
                    <Text color="gray.400" fontSize="sm">
                      Enter your research query and let our AI agents find comprehensive information
                    </Text>
                  </CardHeader>
                  <CardBody>
                    <VStack spacing={6}>
                      <FormControl>
                        <FormLabel color="gray.300">Research Query</FormLabel>
                        <Textarea
                          value={query}
                          onChange={(e) => setQuery(e.target.value)}
                          placeholder="e.g., Find aerospace bolts compatible with Boeing 737-800 hydraulic systems..."
                          bg="black"
                          color="white"
                          borderColor="gray.600"
                          _hover={{ borderColor: 'gray.500' }}
                          _focus={{ borderColor: 'brand.500', boxShadow: '0 0 0 1px var(--chakra-colors-brand-500)' }}
                          rows={4}
                        />
                      </FormControl>

                      <HStack spacing={4} w="full">
                        <FormControl>
                          <FormLabel color="gray.300">Priority</FormLabel>
                          <Select
                            value={priority}
                            onChange={(e) => setPriority(e.target.value as any)}
                            bg="black"
                            color="white"
                            borderColor="gray.600"
                            _hover={{ borderColor: 'gray.500' }}
                            _focus={{ borderColor: 'brand.500' }}
                          >
                            <option value="low">Low</option>
                            <option value="normal">Normal</option>
                            <option value="high">High</option>
                            <option value="urgent">Urgent</option>
                          </Select>
                        </FormControl>

                        <FormControl>
                          <FormLabel color="gray.300">Research Depth</FormLabel>
                          <Select
                            value={researchDepth}
                            onChange={(e) => setResearchDepth(e.target.value as any)}
                            bg="black"
                            color="white"
                            borderColor="gray.600"
                            _hover={{ borderColor: 'gray.500' }}
                            _focus={{ borderColor: 'brand.500' }}
                          >
                            <option value="quick">Quick (5-10 min)</option>
                            <option value="standard">Standard (15-30 min)</option>
                            <option value="deep">Deep (45-60 min)</option>
                          </Select>
                        </FormControl>
                      </HStack>

                      <Button
                        colorScheme="brand"
                        size="lg"
                        leftIcon={<FiSend />}
                        onClick={startResearchSession}
                        isLoading={isStarting}
                        loadingText="Starting Research..."
                        isDisabled={!query.trim() || connectionStatus !== 'connected'}
                        w="full"
                      >
                        Start Research Session
                      </Button>
                    </VStack>
                  </CardBody>
                </Card>
              </TabPanel>

              {/* Research Sessions Tab */}
              <TabPanel>
                <VStack spacing={6}>
                  <HStack w="full">
                    <Heading size="md" color="gray.100">
                      Your Research Sessions
                    </Heading>
                    <Spacer />
                    <Button
                      size="sm"
                      leftIcon={<FiRefreshCw />}
                      onClick={loadUserSessions}
                      isLoading={isLoadingSessions}
                      variant="outline"
                      colorScheme="brand"
                    >
                      Refresh
                    </Button>
                  </HStack>

                  {!token ? (
                    <Alert status="info" bg="blue.900" borderColor="blue.600" borderWidth="1px">
                      <AlertIcon />
                      <AlertTitle>Authentication Required</AlertTitle>
                      <AlertDescription>
                        Please log in to view your research sessions.
                      </AlertDescription>
                    </Alert>
                  ) : isLoadingSessions ? (
                    <Flex justify="center" py={8}>
                      <Spinner size="lg" color="brand.500" />
                    </Flex>
                  ) : sessions.length === 0 ? (
                    <Card bg="gray.800" borderColor="gray.700" w="full">
                      <CardBody textAlign="center" py={12}>
                        <Text color="gray.400" fontSize="lg">
                          No research sessions yet
                        </Text>
                        <Text color="gray.500" fontSize="sm" mt={2}>
                          Start your first research session to see it here
                        </Text>
                      </CardBody>
                    </Card>
                  ) : (
                    <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6} w="full">
                      {sessions.map((session) => (
                        <Card key={session.id} bg="gray.800" borderColor="gray.700">
                          <CardBody>
                            <VStack spacing={4} align="start">
                              <HStack w="full">
                                <Badge
                                  colorScheme={getStatusColor(session.status)}
                                  variant="subtle"
                                  display="flex"
                                  alignItems="center"
                                  gap={1}
                                >
                                  {getStatusIcon(session.status)}
                                  {session.status.toUpperCase()}
                                </Badge>
                                <Spacer />
                                <Text color="gray.500" fontSize="xs">
                                  {formatTimestamp(session.created_at)}
                                </Text>
                              </HStack>

                              <Text color="gray.100" fontSize="sm" fontWeight="medium" noOfLines={3}>
                                {session.query}
                              </Text>

                              {session.results && session.results.length > 0 && (
                                <Text color="gray.400" fontSize="xs">
                                  {session.results.length} results found
                                </Text>
                              )}

                              <HStack spacing={2} w="full">
                                <Button
                                  size="sm"
                                  leftIcon={<FiEye />}
                                  onClick={() => viewSessionDetails(session)}
                                  variant="outline"
                                  colorScheme="brand"
                                  flex={1}
                                >
                                  View
                                </Button>
                                {session.results && session.results.length > 0 && (
                                  <IconButton
                                    size="sm"
                                    icon={<FiDownload />}
                                    aria-label="Export results"
                                    variant="outline"
                                    colorScheme="green"
                                  />
                                )}
                              </HStack>
                            </VStack>
                          </CardBody>
                        </Card>
                      ))}
                    </SimpleGrid>
                  )}
                </VStack>
              </TabPanel>
            </TabPanels>
          </Tabs>
          </VStack>
        </Container>
      </Box>

      {/* Session Details Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="4xl">
        <ModalOverlay />
        <ModalContent bg="gray.800" borderColor="gray.700">
          <ModalHeader color="gray.100">
            Research Session Details
          </ModalHeader>
          <ModalCloseButton color="gray.400" />
          <ModalBody pb={6}>
            {selectedSession && (
              <VStack spacing={6} align="start">
                <Box w="full">
                  <Text color="gray.400" fontSize="sm" mb={2}>Query</Text>
                  <Text color="gray.100" fontSize="md" p={3} bg="gray.900" borderRadius="md">
                    {selectedSession.query}
                  </Text>
                </Box>

                <HStack spacing={4}>
                  <Badge
                    colorScheme={getStatusColor(selectedSession.status)}
                    variant="subtle"
                    display="flex"
                    alignItems="center"
                    gap={1}
                  >
                    {getStatusIcon(selectedSession.status)}
                    {selectedSession.status.toUpperCase()}
                  </Badge>
                  <Text color="gray.400" fontSize="sm">
                    Created: {formatTimestamp(selectedSession.created_at)}
                  </Text>
                </HStack>

                {selectedSession.results && selectedSession.results.length > 0 ? (
                  <Box w="full">
                    <Text color="gray.400" fontSize="sm" mb={4}>
                      Results ({selectedSession.results.length})
                    </Text>
                    <VStack spacing={4} align="start" w="full">
                      {selectedSession.results.map((result, index) => (
                        <Card key={index} bg="gray.900" borderColor="gray.600" w="full">
                          <CardBody>
                            <VStack spacing={3} align="start">
                              <HStack>
                                <Badge colorScheme="blue" variant="subtle">
                                  {result.type}
                                </Badge>
                                <Text color="gray.500" fontSize="xs">
                                  {formatTimestamp(result.timestamp)}
                                </Text>
                              </HStack>
                              <Text color="gray.200" fontSize="sm">
                                {result.content}
                              </Text>
                              {result.metadata && (
                                <Text color="gray.500" fontSize="xs">
                                  Additional data available
                                </Text>
                              )}
                            </VStack>
                          </CardBody>
                        </Card>
                      ))}
                    </VStack>
                  </Box>
                ) : (
                  <Text color="gray.400" fontSize="sm">
                    No results available yet
                  </Text>
                )}
              </VStack>
            )}
          </ModalBody>
        </ModalContent>
      </Modal>
    </Box>
  )
}

export default ResearchPage
