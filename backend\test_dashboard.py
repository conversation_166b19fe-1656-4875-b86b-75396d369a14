"""
Test script for dashboard API endpoints.
"""
import requests
import json

BASE_URL = "http://localhost:8000/api/v1"

def test_login():
    """Test login to get a token."""
    print("Testing login...")
    data = {
        "email": "<EMAIL>",
        "password": "testpassword123"
    }
    response = requests.post(f"{BASE_URL}/auth/login", json=data)
    print(f"Login Status: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"Login successful! Token: {result['access_token'][:20]}...")
        return result['access_token']
    else:
        print(f"Login failed: {response.text}")
        return None

def test_dashboard_metrics(token):
    """Test dashboard metrics endpoint."""
    print("\nTesting dashboard metrics...")
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/dashboard/metrics", headers=headers)
    print(f"Metrics Status: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print("Metrics data:")
        print(json.dumps(result, indent=2))
        return result
    else:
        print(f"Metrics failed: {response.text}")
        return None

def test_dashboard_activity(token):
    """Test dashboard activity endpoint."""
    print("\nTesting dashboard activity...")
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/dashboard/activity", headers=headers)
    print(f"Activity Status: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"Activity data: {len(result)} items")
        if result:
            print("Sample activity:")
            print(json.dumps(result[0], indent=2))
        return result
    else:
        print(f"Activity failed: {response.text}")
        return None

def test_dashboard_chart_data(token):
    """Test dashboard chart data endpoint."""
    print("\nTesting dashboard chart data...")
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/dashboard/chart-data", headers=headers)
    print(f"Chart Data Status: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"Chart data: {len(result)} data points")
        if result:
            print("Sample data points:")
            print(json.dumps(result[:5], indent=2))
        return result
    else:
        print(f"Chart data failed: {response.text}")
        return None

if __name__ == "__main__":
    print("Testing Dashboard API Endpoints")
    print("=" * 40)
    
    # Test login first
    token = test_login()
    
    if token:
        # Test all dashboard endpoints
        test_dashboard_metrics(token)
        test_dashboard_activity(token)
        test_dashboard_chart_data(token)
    else:
        print("Cannot test dashboard endpoints without authentication token")
    
    print("\nTest completed!")
