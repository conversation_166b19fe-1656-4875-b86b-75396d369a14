import React from 'react'
import {
  Box,
  Container,
  HStack,
  VStack,
  Text,
  Heading,
  Button,
  IconButton,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  Avatar,
  useColorModeValue,
} from '@chakra-ui/react'
import {
  FaChevronDown,
  FaCog,
  FaSearch,
  FaFileAlt,
  FaBell,
} from 'react-icons/fa'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'

interface HeaderProps {
  title?: string
  subtitle?: string
  showNavigation?: boolean
  showUserMenu?: boolean
  children?: React.ReactNode
}

export const Header: React.FC<HeaderProps> = ({
  title,
  subtitle,
  showNavigation = true,
  showUserMenu = true,
  children,
}) => {
  const navigate = useNavigate()
  const { user, logout } = useAuth()

  const handleLogoClick = () => {
    navigate('/dashboard')
  }

  const handleLogout = () => {
    logout()
    navigate('/login')
  }

  return (
    <Box
      bg="gray.800"
      borderBottom="1px"
      borderColor="gray.700"
      px={{ base: 4, md: 6 }}
      py={4}
      boxShadow="0 4px 6px rgba(0, 0, 0, 0.1)"
    >
      <Container maxW="7xl">
        <HStack justify="space-between">
          <HStack spacing={{ base: 2, md: 4 }}>
            {/* K.A.R.E.N. Logo - Clickable */}
            <Box
              w={{ base: 10, md: 12 }}
              h={{ base: 10, md: 12 }}
              bgGradient="gradient.primary"
              borderRadius="xl"
              display="flex"
              alignItems="center"
              justifyContent="center"
              boxShadow="glow"
              cursor="pointer"
              transition="all 0.2s"
              _hover={{
                transform: 'scale(1.05)',
                boxShadow: 'glow, 0 0 20px rgba(99, 102, 241, 0.4)',
              }}
              onClick={handleLogoClick}
            >
              <Text fontSize={{ base: 'lg', md: 'xl' }} fontWeight="bold" color="white">
                K
              </Text>
            </Box>
            
            <VStack align="start" spacing={0} display={{ base: 'none', sm: 'flex' }}>
              <Heading 
                size={{ base: 'md', md: 'lg' }} 
                color="gray.100"
                cursor="pointer"
                onClick={handleLogoClick}
                _hover={{ color: 'brand.300' }}
                transition="color 0.2s"
              >
                K.A.R.E.N.
              </Heading>
              <Text fontSize="xs" color="gray.400">
                {subtitle || 'Knowledgeable Acquisition & Requisition Execution Navigator'}
              </Text>
            </VStack>

            {/* Page Title (if provided) */}
            {title && (
              <>
                <Text color="gray.500" fontSize="lg" mx={2}>
                  /
                </Text>
                <VStack align="start" spacing={0}>
                  <Heading size="md" color="gray.100">
                    {title}
                  </Heading>
                </VStack>
              </>
            )}
          </HStack>

          <HStack spacing={{ base: 2, md: 4 }}>
            {/* Navigation Buttons */}
            {showNavigation && (
              <>
                <Button
                  leftIcon={<FaSearch />}
                  variant="ghost"
                  color="gray.300"
                  _hover={{ bg: 'gray.700' }}
                  onClick={() => navigate('/search')}
                  size={{ base: 'sm', md: 'md' }}
                  display={{ base: 'none', md: 'flex' }}
                >
                  Search
                </Button>
                <Button
                  leftIcon={<FaFileAlt />}
                  variant="ghost"
                  color="gray.300"
                  _hover={{ bg: 'gray.700' }}
                  onClick={() => navigate('/research')}
                  size={{ base: 'sm', md: 'md' }}
                  display={{ base: 'none', lg: 'flex' }}
                >
                  Research
                </Button>
              </>
            )}

            {/* Custom children (like notification center) */}
            {children}

            {/* User Menu */}
            {showUserMenu && user && (
              <Menu>
                <MenuButton
                  as={Button}
                  variant="ghost"
                  rightIcon={<FaChevronDown />}
                  color="gray.300"
                  _hover={{ bg: 'gray.700' }}
                  size={{ base: 'sm', md: 'md' }}
                >
                  <HStack spacing={2}>
                    <Avatar size="sm" name={user.full_name || user.username} />
                    <Text display={{ base: 'none', md: 'block' }}>
                      {user.full_name || user.username}
                    </Text>
                  </HStack>
                </MenuButton>
                <MenuList bg="gray.800" borderColor="gray.700">
                  <MenuItem
                    icon={<FaCog />}
                    onClick={() => navigate('/settings')}
                    bg="gray.800"
                    _hover={{ bg: 'gray.700' }}
                    color="gray.100"
                  >
                    Settings
                  </MenuItem>
                  <MenuItem
                    onClick={handleLogout}
                    bg="gray.800"
                    _hover={{ bg: 'gray.700' }}
                    color="gray.100"
                  >
                    Logout
                  </MenuItem>
                </MenuList>
              </Menu>
            )}
          </HStack>
        </HStack>
      </Container>
    </Box>
  )
}
