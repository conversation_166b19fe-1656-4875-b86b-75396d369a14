"""
Database models for Expendra application.
"""
from datetime import datetime
from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, JSON, ForeignKey
from sqlalchemy.orm import relationship
from database import Base

class User(Base):
    """
    User model for authentication and profile management.
    """
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    email = Column(String(255), unique=True, index=True, nullable=False)
    username = Column(String(100), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    full_name = Column(String(255), nullable=True)
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    settings = relationship("UserSettings", back_populates="user", uselist=False)
    research_sessions = relationship("ResearchSession", back_populates="user")

class UserSettings(Base):
    """
    User settings model for storing LLM provider configurations and UI preferences.
    """
    __tablename__ = "user_settings"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), unique=True, nullable=False)
    
    # LLM Provider Settings
    default_llm_provider = Column(String(50), default="openai")  # openai, anthropic, google, ollama
    openai_api_key = Column(Text, nullable=True)
    anthropic_api_key = Column(Text, nullable=True)
    google_api_key = Column(Text, nullable=True)
    ollama_endpoint = Column(String(255), nullable=True)
    default_model = Column(String(100), default="gpt-3.5-turbo")
    
    # UI/UX Settings
    theme = Column(String(20), default="dark")  # dark, light
    primary_color = Column(String(20), default="blue")
    font_size = Column(String(20), default="medium")  # small, medium, large
    animations_enabled = Column(Boolean, default=True)
    
    # Research Preferences
    default_research_depth = Column(String(20), default="standard")  # quick, standard, deep
    auto_export_format = Column(String(20), default="pdf")  # pdf, excel, both
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    user = relationship("User", back_populates="settings")

class ResearchSession(Base):
    """
    Model for storing research sessions and their results.
    """
    __tablename__ = "research_sessions"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    query = Column(Text, nullable=False)  # Original user query
    status = Column(String(20), default="pending")  # pending, in_progress, completed, failed
    
    # Research context and results
    context_data = Column(JSON, nullable=True)  # IPCs, references, images metadata
    research_results = Column(JSON, nullable=True)  # Structured research findings
    agent_conversations = Column(JSON, nullable=True)  # Multi-agent conversation history
    
    # Metadata
    estimated_duration = Column(Integer, nullable=True)  # in minutes
    actual_duration = Column(Integer, nullable=True)  # in minutes
    priority = Column(String(20), default="normal")  # low, normal, high, urgent
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    completed_at = Column(DateTime, nullable=True)
    
    # Relationships
    user = relationship("User", back_populates="research_sessions")
    findings = relationship("ResearchFinding", back_populates="session")

class ResearchFinding(Base):
    """
    Model for storing individual research findings within a session.
    """
    __tablename__ = "research_findings"
    
    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(Integer, ForeignKey("research_sessions.id"), nullable=False)
    
    finding_type = Column(String(50), nullable=False)  # part_info, pricing, alternative, market_data
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    
    # Structured data
    data = Column(JSON, nullable=False)  # Flexible JSON structure for different finding types
    source_url = Column(String(500), nullable=True)
    source_name = Column(String(255), nullable=True)
    confidence_score = Column(Integer, default=0)  # 0-100
    
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    session = relationship("ResearchSession", back_populates="findings")
