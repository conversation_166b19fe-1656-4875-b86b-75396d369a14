#!/usr/bin/env python3
"""
Simple script to test the research API endpoints.
"""
import requests
import json

BASE_URL = "http://localhost:8000/api/v1/research"

def test_connection():
    """Test the connection endpoint."""
    print("Testing connection...")
    response = requests.get(f"{BASE_URL}/test-connection-public")
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print()

def test_start_research():
    """Test starting a research session."""
    print("Testing start research session...")
    data = {
        "query": "Find aerospace bolts for Boeing 737"
    }
    response = requests.post(f"{BASE_URL}/start-public", json=data)
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    
    if response.status_code == 200:
        session_id = response.json().get("session_id")
        print(f"Session ID: {session_id}")
        return session_id
    return None

def test_session_status(session_id):
    """Test getting session status."""
    if not session_id:
        print("No session ID to test")
        return

    print(f"Testing session status for {session_id}...")
    response = requests.get(f"{BASE_URL}/sessions/{session_id}/status-public")
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print()

if __name__ == "__main__":
    print("=== Testing Expendra Research API ===\n")
    
    # Test connection
    test_connection()
    
    # Test starting research
    session_id = test_start_research()
    
    # Test session status
    test_session_status(session_id)
    
    print("=== Tests completed ===")
