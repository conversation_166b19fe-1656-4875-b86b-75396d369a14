"""
AI Agent Service for managing CrewAI agents and orchestrating research tasks.
"""
import asyncio
import json
import uuid
from typing import Dict, List, Optional, Any
from datetime import datetime
from crewai import Agent, Task, Crew, Process
from langchain_google_genai import ChatGoogleGenerativeAI
from sqlalchemy.orm import Session

from services.llm_service import llm_service
from agents.specialized_agents import AgentFactory, WorkflowTemplates
from tools.web_automation import web_search_tool, web_form_tool, web_scraping_tool
from models import ResearchSession, ResearchFinding, UserSettings
from database import get_db
import logging

logger = logging.getLogger(__name__)

class AgentService:
    """
    Service for managing AI agents and orchestrating research tasks.
    Uses CrewAI for multi-agent coordination.
    """

    def __init__(self):
        self.active_sessions: Dict[str, Dict] = {}
        self.llm = None
        self.agents = {}
        self.tools = []
        self._initialize_agents()

    def _initialize_agents(self):
        """Initialize LLM and create specialized agents."""
        try:
            # Initialize LLM with Google Gemini
            import os
            api_key = os.getenv("GOOGLE_API_KEY")
            if api_key:
                self.llm = ChatGoogleGenerativeAI(
                    model="gemini-2.0-flash-001",
                    google_api_key=api_key,
                    temperature=0.7
                )

                # Initialize tools
                self.tools = [web_search_tool, web_form_tool, web_scraping_tool]

                # Create specialized agents
                self.agents = {
                    "sourcing_specialist": AgentFactory.create_sourcing_specialist(
                        llm=self.llm,
                        tools=self.tools
                    ),
                    "research_analyst": AgentFactory.create_research_analyst(
                        llm=self.llm,
                        tools=self.tools
                    ),
                    "data_processor": AgentFactory.create_data_processor(
                        llm=self.llm,
                        tools=[]  # Data processor mainly analyzes
                    )
                }

                logger.info("CrewAI agents initialized successfully")
            else:
                logger.warning("GOOGLE_API_KEY not found - agents will use fallback mode")

        except Exception as e:
            logger.error(f"Failed to initialize agents: {e}")
            # Continue with simplified mode if agent initialization fails

    async def start_research_session(self, query: str, user_id: str = "default") -> str:
        """
        Start a new research session using real Gemini API.

        Args:
            query: Research query
            user_id: User identifier

        Returns:
            Session ID
        """
        session_id = str(uuid.uuid4())

        # Create a session in memory
        session_data = {
            "id": session_id,
            "session_id": session_id,  # Add for compatibility with ResearchStatusResponse
            "query": query,
            "user_id": user_id,
            "status": "running",
            "created_at": datetime.now().isoformat(),
            "start_time": datetime.now().isoformat(),  # Add for ResearchStatusResponse
            "progress": {"stage": "starting", "percentage": 0},  # Add for ResearchStatusResponse
            "results": []
        }

        self.active_sessions[session_id] = session_data

        # Also save to database
        try:
            db = next(get_db())
            db_session = ResearchSession(
                title=f"Research: {query[:50]}...",  # Truncate title if too long
                description=f"AI research session for: {query}",
                query=query,
                user_id=int(user_id) if user_id.isdigit() else 1,  # Default to user 1 for non-numeric IDs
                status="in_progress",
                priority="normal",
                context_data={"session_uuid": session_id}  # Store UUID for linking
            )
            db.add(db_session)
            db.commit()
            db.refresh(db_session)

            # Store the database ID in memory session for reference
            session_data["db_id"] = db_session.id

            logger.info(f"Created database session {db_session.id} for UUID {session_id}")

        except Exception as e:
            logger.error(f"Failed to save session to database: {e}")
            # Continue with in-memory session even if DB save fails
        finally:
            db.close()

        try:
            # Use CrewAI agents if available, otherwise fallback to simple LLM
            if self.agents and self.llm:
                response = await self._run_crew_research(query, session_data)
            else:
                # Fallback to simple LLM research
                research_prompt = f"""
                You are an AI research assistant specializing in aerospace and industrial parts sourcing.

                Research Query: {query}

                Please provide a comprehensive research response that includes:
                1. Key findings related to the query
                2. Potential suppliers or sources
                3. Technical specifications if applicable
                4. Market insights and pricing considerations
                5. Recommendations for next steps

                Format your response in a clear, structured manner.
                """

                response = await llm_service.generate_content(
                    prompt=research_prompt,
                    model="gemini-2.0-flash-001"
                )

            if response["status"] == "success":
                session_data["results"].append({
                    "type": "ai_research",
                    "content": response["content"],
                    "timestamp": datetime.now().isoformat(),
                    "model_used": response["model"],
                    "token_usage": response.get("usage", {})
                })
                session_data["status"] = "completed"
                session_data["progress"] = {"stage": "completed", "percentage": 100}
                session_data["result"] = response["content"]

                # Update database session
                self._update_db_session(session_data, "completed", response["content"])
            else:
                session_data["results"].append({
                    "type": "error",
                    "content": f"Research failed: {response.get('error', 'Unknown error')}",
                    "timestamp": datetime.now().isoformat()
                })
                session_data["status"] = "failed"
                session_data["progress"] = {"stage": "failed", "percentage": 0}
                session_data["error"] = response.get('error', 'Unknown error')

                # Update database session
                self._update_db_session(session_data, "failed", None)

        except Exception as e:
            logger.error(f"Research session failed: {e}")
            session_data["results"].append({
                "type": "error",
                "content": f"Research session failed: {str(e)}",
                "timestamp": datetime.now().isoformat()
            })
            session_data["status"] = "failed"
            session_data["progress"] = {"stage": "failed", "percentage": 0}
            session_data["error"] = str(e)

            # Update database session
            self._update_db_session(session_data, "failed", None)

        return session_id

    async def _run_crew_research(self, query: str, session_data: Dict) -> Dict[str, Any]:
        """
        Run research using CrewAI multi-agent system.

        Args:
            query: Research query
            session_data: Session tracking data

        Returns:
            Research response in standard format
        """
        try:
            # Update progress
            session_data["progress"] = {"stage": "initializing_crew", "percentage": 10}

            # Create tasks for the crew
            sourcing_task = Task(
                description=f"""
                Research and find sources for: {query}

                Your task is to:
                1. Identify the specific parts, tools, or materials mentioned
                2. Find potential suppliers and sources
                3. Gather pricing and availability information
                4. Look for alternatives and compatible substitutes
                5. Verify technical specifications

                Provide detailed findings with sources and links where possible.
                """,
                agent=self.agents["sourcing_specialist"],
                expected_output="Detailed sourcing findings with suppliers, pricing, and alternatives"
            )

            research_task = Task(
                description=f"""
                Analyze the sourcing findings and conduct market research for: {query}

                Your task is to:
                1. Review the sourcing specialist's findings
                2. Analyze market trends and supplier capabilities
                3. Assess risks and opportunities
                4. Compare pricing across different suppliers
                5. Provide strategic insights

                Focus on providing actionable market intelligence.
                """,
                agent=self.agents["research_analyst"],
                expected_output="Comprehensive market analysis with strategic insights",
                context=[sourcing_task]  # Depends on sourcing task
            )

            processing_task = Task(
                description=f"""
                Process and synthesize all research findings for: {query}

                Your task is to:
                1. Organize all findings from sourcing and research phases
                2. Create clear comparisons and recommendations
                3. Highlight key decision points
                4. Format results for easy decision-making
                5. Provide executive summary

                Present in a clear, actionable format.
                """,
                agent=self.agents["data_processor"],
                expected_output="Structured final report with recommendations",
                context=[sourcing_task, research_task]  # Depends on both previous tasks
            )

            # Update progress
            session_data["progress"] = {"stage": "running_crew", "percentage": 30}

            # Create and run the crew
            crew = Crew(
                agents=[
                    self.agents["sourcing_specialist"],
                    self.agents["research_analyst"],
                    self.agents["data_processor"]
                ],
                tasks=[sourcing_task, research_task, processing_task],
                process=Process.sequential,
                verbose=True,
                memory=True
            )

            # Execute the crew
            session_data["progress"] = {"stage": "executing_tasks", "percentage": 50}
            result = crew.kickoff()

            session_data["progress"] = {"stage": "processing_results", "percentage": 90}

            return {
                "status": "success",
                "content": str(result),
                "model": "crewai-multi-agent",
                "usage": {
                    "agents_used": ["sourcing_specialist", "research_analyst", "data_processor"],
                    "tasks_completed": 3
                }
            }

        except Exception as e:
            logger.error(f"CrewAI research failed: {e}")
            return {
                "status": "error",
                "error": f"Multi-agent research failed: {str(e)}",
                "model": "crewai-multi-agent"
            }

    def get_session_status(self, session_id: str) -> Optional[Dict]:
        """
        Get the status of a research session.

        Args:
            session_id: Session identifier

        Returns:
            Session status data or None if not found
        """
        return self.active_sessions.get(session_id)

    def get_all_sessions(self, user_id: str = "default") -> List[Dict]:
        """
        Get all sessions for a user.

        Args:
            user_id: User identifier

        Returns:
            List of session data
        """
        return [
            session for session in self.active_sessions.values()
            if session.get("user_id") == user_id
        ]

    def _update_db_session(self, session_data: Dict, status: str, result: Optional[str] = None):
        """
        Update the database session with completion status and results.

        Args:
            session_data: In-memory session data
            status: New status (completed, failed)
            result: Research result content (if successful)
        """
        try:
            if "db_id" not in session_data:
                logger.warning(f"No database ID found for session {session_data.get('session_id')}")
                return

            db = next(get_db())
            db_session = db.query(ResearchSession).filter(
                ResearchSession.id == session_data["db_id"]
            ).first()

            if db_session:
                db_session.status = status
                db_session.research_results = {
                    "content": result,
                    "results": session_data.get("results", []),
                    "progress": session_data.get("progress", {}),
                    "session_uuid": session_data.get("session_id")
                }
                if status == "completed":
                    db_session.completed_at = datetime.utcnow()

                db.commit()
                logger.info(f"Updated database session {db_session.id} with status {status}")
            else:
                logger.warning(f"Database session {session_data['db_id']} not found")

        except Exception as e:
            logger.error(f"Failed to update database session: {e}")
        finally:
            db.close()

    async def test_connection(self) -> Dict[str, Any]:
        """
        Test the connection to LLM services.

        Returns:
            Connection test results
        """
        try:
            # Test LLM service connection
            result = await llm_service.test_connection()
            return {
                "status": "success",
                "llm_service": result,
                "message": "All services connected successfully"
            }
        except Exception as e:
            return {
                "status": "error",
                "message": f"Connection test failed: {str(e)}"
            }

# Create global instance
agent_service = AgentService()
