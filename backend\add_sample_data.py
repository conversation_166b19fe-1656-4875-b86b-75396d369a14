"""
Add sample research sessions to make the dashboard more interesting.
"""
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from database import get_db, SessionLocal
from models import ResearchSession, User
import random

def add_sample_research_sessions():
    """Add sample research sessions for testing."""
    db = SessionLocal()
    
    try:
        # Get the test user
        user = db.query(User).filter(User.email == "<EMAIL>").first()
        if not user:
            print("Test user not found. Please login first.")
            return
        
        print(f"Adding sample data for user: {user.username}")
        
        # Sample research sessions with different statuses and dates
        sample_sessions = [
            {
                "title": "Boeing 737 Hydraulic Components Research",
                "description": "Comprehensive analysis of hydraulic system components for Boeing 737 aircraft",
                "query": "Boeing 737 hydraulic pump alternatives and suppliers",
                "status": "completed",
                "days_ago": 1
            },
            {
                "title": "Aerospace Fasteners Market Analysis",
                "description": "Market research for titanium and steel fasteners in aerospace industry",
                "query": "aerospace fasteners titanium steel market analysis suppliers",
                "status": "completed",
                "days_ago": 2
            },
            {
                "title": "Avionics Component Sourcing",
                "description": "Research on avionics components for commercial aircraft",
                "query": "avionics components commercial aircraft sourcing",
                "status": "in_progress",
                "days_ago": 0
            },
            {
                "title": "Landing Gear Parts Investigation",
                "description": "Investigation of landing gear components and maintenance parts",
                "query": "landing gear parts maintenance components aircraft",
                "status": "completed",
                "days_ago": 3
            },
            {
                "title": "Engine Component Analysis",
                "description": "Analysis of engine components for maintenance and replacement",
                "query": "aircraft engine components maintenance replacement parts",
                "status": "pending",
                "days_ago": 0
            },
            {
                "title": "Cabin Interior Materials Research",
                "description": "Research on cabin interior materials and suppliers",
                "query": "cabin interior materials aircraft suppliers certification",
                "status": "completed",
                "days_ago": 5
            },
            {
                "title": "Navigation Equipment Study",
                "description": "Study of navigation equipment and GPS systems",
                "query": "navigation equipment GPS systems aircraft avionics",
                "status": "completed",
                "days_ago": 7
            },
            {
                "title": "Safety Equipment Procurement",
                "description": "Procurement research for safety equipment and emergency systems",
                "query": "safety equipment emergency systems aircraft procurement",
                "status": "in_progress",
                "days_ago": 1
            },
            {
                "title": "Composite Materials Analysis",
                "description": "Analysis of composite materials for aircraft structures",
                "query": "composite materials aircraft structures carbon fiber",
                "status": "completed",
                "days_ago": 10
            },
            {
                "title": "Maintenance Tools Research",
                "description": "Research on specialized maintenance tools and equipment",
                "query": "aircraft maintenance tools specialized equipment suppliers",
                "status": "completed",
                "days_ago": 14
            }
        ]
        
        # Add sessions to database
        for session_data in sample_sessions:
            # Calculate creation date
            created_at = datetime.utcnow() - timedelta(days=session_data["days_ago"])
            
            # Add some random hours/minutes to spread throughout the day
            created_at = created_at.replace(
                hour=random.randint(8, 18),
                minute=random.randint(0, 59),
                second=random.randint(0, 59)
            )
            
            session = ResearchSession(
                user_id=user.id,
                title=session_data["title"],
                description=session_data["description"],
                query=session_data["query"],
                status=session_data["status"],
                created_at=created_at,
                updated_at=created_at
            )
            
            db.add(session)
        
        db.commit()
        print(f"Successfully added {len(sample_sessions)} sample research sessions!")
        
        # Print summary
        total_sessions = db.query(ResearchSession).filter(ResearchSession.user_id == user.id).count()
        completed_sessions = db.query(ResearchSession).filter(
            ResearchSession.user_id == user.id,
            ResearchSession.status == "completed"
        ).count()
        in_progress_sessions = db.query(ResearchSession).filter(
            ResearchSession.user_id == user.id,
            ResearchSession.status == "in_progress"
        ).count()
        pending_sessions = db.query(ResearchSession).filter(
            ResearchSession.user_id == user.id,
            ResearchSession.status == "pending"
        ).count()
        
        print(f"\nDatabase Summary:")
        print(f"Total sessions: {total_sessions}")
        print(f"Completed: {completed_sessions}")
        print(f"In Progress: {in_progress_sessions}")
        print(f"Pending: {pending_sessions}")
        
    except Exception as e:
        print(f"Error adding sample data: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    print("Adding sample research sessions...")
    add_sample_research_sessions()
    print("Done!")
