#!/usr/bin/env python3
"""
Test script for Expendra API endpoints
"""
import requests
import json

BASE_URL = "http://localhost:8000/api/v1"

def test_registration():
    """Test user registration"""
    print("🧪 Testing user registration...")
    
    url = f"{BASE_URL}/auth/register"
    data = {
        "email": "<EMAIL>",
        "username": "testuser",
        "password": "testpass123",
        "full_name": "Test User"
    }
    
    try:
        response = requests.post(url, json=data)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
        
        if response.status_code == 201:
            print("✅ Registration successful!")
            return response.json()
        else:
            print("❌ Registration failed!")
            return None
    except Exception as e:
        print(f"❌ Registration error: {e}")
        return None

def test_login():
    """Test user login"""
    print("\n🧪 Testing user login...")
    
    url = f"{BASE_URL}/auth/login"
    data = {
        "email": "<EMAIL>",
        "password": "testpassword123"
    }
    
    try:
        response = requests.post(url, json=data)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
        
        if response.status_code == 200:
            print("✅ Login successful!")
            return response.json()
        else:
            print("❌ Login failed!")
            return None
    except Exception as e:
        print(f"❌ Login error: {e}")
        return None

def test_protected_route(token):
    """Test accessing a protected route"""
    print("\n🧪 Testing protected route...")
    
    url = f"{BASE_URL}/auth/me"
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(url, headers=headers)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
        
        if response.status_code == 200:
            print("✅ Protected route access successful!")
            return response.json()
        else:
            print("❌ Protected route access failed!")
            return None
    except Exception as e:
        print(f"❌ Protected route error: {e}")
        return None

def test_settings_endpoints(token):
    """Test settings endpoints"""
    print("\n🧪 Testing settings endpoints...")
    
    # Test getting settings
    url = f"{BASE_URL}/settings"
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(url, headers=headers)
        print(f"Get Settings - Status Code: {response.status_code}")
        print(f"Get Settings - Response: {response.json()}")
        
        if response.status_code == 200:
            print("✅ Get settings successful!")
        else:
            print("❌ Get settings failed!")
            
    except Exception as e:
        print(f"❌ Get settings error: {e}")

def main():
    """Run all tests"""
    print("🚀 Starting Expendra API Tests\n")
    
    # Test registration
    registration_result = test_registration()
    
    # Test login
    login_result = test_login()
    
    if login_result and "access_token" in login_result:
        token = login_result["access_token"]
        
        # Test protected route
        test_protected_route(token)
        
        # Test settings
        test_settings_endpoints(token)
    else:
        print("❌ Cannot test protected routes without valid token")
    
    print("\n🏁 API Tests Complete!")

if __name__ == "__main__":
    main()
