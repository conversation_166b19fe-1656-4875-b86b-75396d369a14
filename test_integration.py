#!/usr/bin/env python3
"""
Integration test script to verify frontend-backend communication.
"""
import requests
import json
import time

def test_backend_health():
    """Test backend health endpoint."""
    print("🔍 Testing backend health...")
    try:
        response = requests.get("http://localhost:8000/health")
        if response.status_code == 200:
            print("✅ Backend is healthy")
            return True
        else:
            print(f"❌ Backend health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Backend connection failed: {e}")
        return False

def test_research_api():
    """Test research API endpoints."""
    print("\n🔬 Testing research API...")
    
    # Test connection
    try:
        response = requests.get("http://localhost:8000/api/v1/research/test-connection-public")
        if response.status_code == 200:
            print("✅ Research API connection successful")
            data = response.json()
            print(f"   Provider: {data.get('llm_service', {}).get('provider', 'unknown')}")
            print(f"   Model: {data.get('llm_service', {}).get('model', 'unknown')}")
        else:
            print(f"❌ Research API connection failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Research API connection error: {e}")
        return False
    
    # Test starting a research session
    try:
        print("\n🚀 Starting test research session...")
        data = {
            "query": "Test integration query for aerospace parts"
        }
        response = requests.post(
            "http://localhost:8000/api/v1/research/start-public",
            json=data
        )
        
        if response.status_code == 200:
            result = response.json()
            session_id = result.get("session_id")
            print(f"✅ Research session started: {session_id}")
            
            # Test getting session status
            print("📊 Checking session status...")
            status_response = requests.get(
                f"http://localhost:8000/api/v1/research/sessions/{session_id}/status-public"
            )
            
            if status_response.status_code == 200:
                status_data = status_response.json()
                print(f"✅ Session status: {status_data.get('status')}")
                print(f"   Query: {status_data.get('query')}")
                print(f"   Results: {len(status_data.get('results', []))} items")
                return True
            else:
                print(f"❌ Session status check failed: {status_response.status_code}")
                return False
        else:
            print(f"❌ Failed to start research session: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Research session error: {e}")
        return False

def test_frontend():
    """Test frontend availability."""
    print("\n🌐 Testing frontend...")
    try:
        response = requests.get("http://localhost:5173")
        if response.status_code == 200:
            print("✅ Frontend is accessible")
            return True
        else:
            print(f"❌ Frontend not accessible: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Frontend connection failed: {e}")
        return False

def main():
    """Run all integration tests."""
    print("🧪 Expendra Integration Test Suite")
    print("=" * 50)
    
    tests = [
        ("Backend Health", test_backend_health),
        ("Research API", test_research_api),
        ("Frontend", test_frontend),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name} test...")
        if test_func():
            passed += 1
        time.sleep(1)  # Brief pause between tests
    
    print("\n" + "=" * 50)
    print(f"🏁 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Integration is working correctly.")
        print("\n📝 Next steps:")
        print("   1. Open http://localhost:5173 in your browser")
        print("   2. Log <NAME_EMAIL> / testpassword123")
        print("   3. Navigate to the Research page to test the new functionality")
        print("   4. Try the Search page for quick research queries")
    else:
        print("⚠️  Some tests failed. Please check the output above.")
    
    return passed == total

if __name__ == "__main__":
    main()
