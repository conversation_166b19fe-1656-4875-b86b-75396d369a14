# Expendra Project Plan

## Project Overview

Expendra is an AI-powered purchasing assistant designed for aerospace buyers, with capabilities extending to non-aviation items and materials. It focuses on sourcing, pricing, alternate part number search, FAA PMA alternatives, and deep market research. The system will feature a modern, intuitive user interface inspired by OpenAI's design, robust backend services, multi-agent AI capabilities orchestrated by CrewAI, and scalable deployment on AWS.

## Name and Repository

*   **Project Name:** Expendra
*   **Repository:** `https://github.com/PapaBear1981/Expendra`

## Tech Stack

*   **Frontend:** React with TypeScript, Chakra UI (for modern UI, theming, animations, dark mode).
*   **Backend:** Python with FastAPI (performant API, good for async LLM calls and web scraping).
*   **AI Orchestration & Multi-Agent Framework:** CrewAI (for defining agent roles, tasks, and workflows) integrated with LangChain (for LLM connections, tool abstractions, memory management).
*   **Web Scraping/Automation:** Playwright (reliable interaction with web portals like SkyGeek, Grainger, PartsBase).
*   **Database:** PostgreSQL with SQLAlchemy (for storing search findings, research data, user configurations, and conversational history).
*   **File Generation:** `pandas` for Excel exports, `ReportLab` for PDF outputs.
*   **LLM Providers:** Support for OpenAI, Google Gemini, Anthropic, Ollama, etc., managed via LangChain.

## Project Phases

### Phase 1: Project Foundation & Core Setup
    *   Initialize project structure for frontend (React/TypeScript) and backend (Python/FastAPI).
    *   Set up version control (e.g., Git) and link to the repository `https://github.com/PapaBear1981/Expendra`.
    *   Create initial documentation files: `README.md`, `PRD.md`, `rules.md`.
    *   Define basic folder structures for frontend, backend, and shared modules.
    *   Set up development environments, including necessary SDKs and libraries.
    *   Implement basic backend health check endpoints.

### Phase 2: Authentication & User Settings
    *   Develop a simple backend authentication system (e.g., JWT-based).
    *   Create the login/registration UI, inspired by the provided example image.
    *   Implement the settings page UI for LLM provider selection, model, API keys, and UI customization (theme, color).
    *   Develop backend endpoints to save/retrieve user settings.

### Phase 3: AI Agent Core, Multi-Agent Orchestration & Web Interaction Tools (with CrewAI)
    *   Integrate LangChain for LLM connections and tool definitions.
    *   Integrate CrewAI to define agent roles (e.g., "Sourcing Specialist", "Research Analyst", "Data Processor"), their specific goals, and the tasks they will perform.
    *   Develop workflows using the CrewAI framework to manage multi-turn conversations and task delegation between agents.
    *   Implement conversational memory management (leveraging LangChain's memory components) within the CrewAI framework.
    *   Set up initial tools (web scrapers using Playwright, data processors) that these agents can utilize.
    *   Develop core agent logic for natural language query processing and tool invocation.
    *   Implement functionality to output findings in PDF and Excel formats.

### Phase 4: Frontend UI/UX Development (Dashboard, Research, Search)
    *   Develop the chatbot-style interface for agent interaction, mirroring OpenAI's UI.
    *   Create the deep research page for monitoring ongoing assignments.
    *   Build the parts/tools/materials search page with natural language input.
    *   Implement the dashboard for an overview of research status.
    *   Apply animations, modern fonts, and ensure a professional, inspired login/landing page.

### Phase 5: Advanced Functionality and Database Integration
    *   Develop specific Playwright scripts for target sites (SkyGeek, Grainger, PartsBase, etc.).
    *   Implement sophisticated "deep market research" logic, potentially involving complex scraping and LLM analysis.
    *   Integrate the PostgreSQL database using SQLAlchemy for storing detailed search results, research findings, and usage history.
    *   Refine PDF and Excel export features to include structured data from the database.
    *   Add support for context input (IPCs, references, images) into agent interactions.

### Phase 6: AWS Deployment and CI/CD with GitHub Actions
    *   Containerize frontend (e.g., Docker with Nginx) and backend (Python/FastAPI).
    *   Set up AWS infrastructure:
        *   Frontend: S3 for static hosting, CloudFront for CDN.
        *   Backend: ECS or EC2 for hosting the FastAPI application, RDS for PostgreSQL.
        *   Consider API Gateway and Lambda for specific microservices.
    *   Configure CI/CD pipelines using GitHub Actions to automate building, testing, and deploying the application to AWS.
    *   Implement secure environment variable management (AWS Systems Manager Parameter Store or Secrets Manager).
    *   Set up logging and monitoring.

## Architecture Overview (Mermaid Diagram)

```mermaid
graph TD
    A[User Browser] --> B(Frontend App - React/Chakra UI);
    B --> C{AWS API Gateway/CloudFront};
    C --> D[Backend Service - Python FastAPI];
    D --> E(LLM Provider APIs - OpenAI, Gemini, etc.);
    D --> F[Database - PostgreSQL on RDS];
    D --> G(CrewAI Orchestration + LangChain Tools/Memory);
    H[GitHub Actions CI/CD Pipeline] --> AWS_Infra(AWS Services);
    AWS_Infra --> C;
    AWS_Infra --> D;
    AWS_Infra --> F;
    G --> H_Agent1[Agent: Sourcing Specialist];
    G --> H_Agent2[Agent: Research Analyst];
    G --> H_Agent3[Agent: Data Processor];
    H_Agent1 --> I(Interacts with Target Sites via Playwright);
    H_Agent2 --> J(Performs Deep Market Research);
    H_Agent3 --> K(Generates PDF/Excel Reports);
    I --> F;
    J --> F;
    K --> F;

    subgraph Frontend Layer
    B
    end

    subgraph Backend Layer
    D
    F
    G
    H_Agent1
    H_Agent2
    H_Agent3
    I
    J
    K
    end

    subgraph External Services
    C
    E
    end

    subgraph CI/CD & Cloud
    H
    AWS_Infra
    end

    classDef frontend fill:#e9f7fe,stroke:#007bff;
    classDef backend fill:#e9ffe9,stroke:#4CAF50;
    classDef cloud fill:#fffbe6,stroke:#ffeb3b;
    classDef cicd fill:#fde9ff,stroke:#ad1457;

    class B frontend;
    class D,F,G,H_Agent1,H_Agent2,H_Agent3,I,J,K backend;
    class C,E cloud;
    class H,AWS_Infra cicd;