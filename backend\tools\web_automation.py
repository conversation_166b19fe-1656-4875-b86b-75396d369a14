"""
Web automation tools using <PERSON>wright for AI agents.
"""
import asyncio
import json
import os
from typing import Dict, List, Optional, Any, TYPE_CHECKING

if TYPE_CHECKING:
    from playwright.async_api import Browser, Page

try:
    from playwright.async_api import async_playwright, <PERSON>, Browser
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False
    # Define dummy types for when Playwright is not available
    Browser = Any
    Page = Any

from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field
import logging

logger = logging.getLogger(__name__)

class WebSearchInput(BaseModel):
    """Input for web search tool."""
    query: str = Field(description="Search query to execute")
    site: Optional[str] = Field(default=None, description="Specific site to search (e.g., 'skygeek.com')")
    max_results: int = Field(default=10, description="Maximum number of results to return")

class WebFormInput(BaseModel):
    """Input for web form filling tool."""
    url: str = Field(description="URL of the page with the form")
    form_data: Dict[str, str] = Field(description="Form field data as key-value pairs")
    submit_button_selector: Optional[str] = Field(default=None, description="CSS selector for submit button")

class WebScrapingInput(BaseModel):
    """Input for web scraping tool."""
    url: str = Field(description="URL to scrape")
    selectors: Dict[str, str] = Field(description="CSS selectors for data extraction")
    wait_for_selector: Optional[str] = Field(default=None, description="Selector to wait for before scraping")

class PlaywrightWebTool(BaseTool):
    """Base class for Playwright-based web tools."""
    
    def __init__(self):
        super().__init__()
        self.browser: Optional[Browser] = None
        self.headless = os.getenv("PLAYWRIGHT_HEADLESS", "true").lower() == "true"
        self.timeout = int(os.getenv("PLAYWRIGHT_TIMEOUT", "30000"))
    
    async def _get_browser(self) -> Browser:
        """Get or create browser instance."""
        if not self.browser:
            playwright = await async_playwright().start()
            self.browser = await playwright.chromium.launch(headless=self.headless)
        return self.browser
    
    async def _close_browser(self):
        """Close browser instance."""
        if self.browser:
            await self.browser.close()
            self.browser = None

class WebSearchTool(PlaywrightWebTool):
    """Tool for performing web searches."""

    name: str = "web_search"
    description: str = "Search the web for information about parts, tools, or materials"
    args_schema: type = WebSearchInput
    
    def _run(self, query: str, site: Optional[str] = None, max_results: int = 10) -> str:
        """Synchronous wrapper for async search."""
        return asyncio.run(self._async_run(query, site, max_results))
    
    async def _async_run(self, query: str, site: Optional[str] = None, max_results: int = 10) -> str:
        """Perform web search and return results."""
        try:
            browser = await self._get_browser()
            page = await browser.new_page()
            
            # Construct search query
            search_query = f"site:{site} {query}" if site else query
            search_url = f"https://www.google.com/search?q={search_query}"
            
            await page.goto(search_url)
            await page.wait_for_selector("div#search", timeout=self.timeout)
            
            # Extract search results
            results = []
            search_results = await page.query_selector_all("div.g")
            
            for i, result in enumerate(search_results[:max_results]):
                try:
                    title_elem = await result.query_selector("h3")
                    link_elem = await result.query_selector("a")
                    snippet_elem = await result.query_selector("span[data-ved]")
                    
                    title = await title_elem.inner_text() if title_elem else "No title"
                    link = await link_elem.get_attribute("href") if link_elem else "No link"
                    snippet = await snippet_elem.inner_text() if snippet_elem else "No snippet"
                    
                    results.append({
                        "title": title,
                        "link": link,
                        "snippet": snippet
                    })
                except Exception as e:
                    logger.warning(f"Error extracting result {i}: {e}")
                    continue
            
            await page.close()
            return json.dumps(results, indent=2)
            
        except Exception as e:
            logger.error(f"Web search error: {e}")
            return f"Error performing web search: {str(e)}"

class WebFormTool(PlaywrightWebTool):
    """Tool for filling out web forms."""

    name: str = "web_form_fill"
    description: str = "Fill out and submit web forms on target websites"
    args_schema: type = WebFormInput
    
    def _run(self, url: str, form_data: Dict[str, str], submit_button_selector: Optional[str] = None) -> str:
        """Synchronous wrapper for async form filling."""
        return asyncio.run(self._async_run(url, form_data, submit_button_selector))
    
    async def _async_run(self, url: str, form_data: Dict[str, str], submit_button_selector: Optional[str] = None) -> str:
        """Fill out a web form and optionally submit it."""
        try:
            browser = await self._get_browser()
            page = await browser.new_page()
            
            await page.goto(url)
            await page.wait_for_load_state("networkidle")
            
            # Fill form fields
            filled_fields = []
            for field_name, value in form_data.items():
                try:
                    # Try different selector strategies
                    selectors = [
                        f"input[name='{field_name}']",
                        f"input[id='{field_name}']",
                        f"textarea[name='{field_name}']",
                        f"select[name='{field_name}']"
                    ]
                    
                    field_filled = False
                    for selector in selectors:
                        element = await page.query_selector(selector)
                        if element:
                            await element.fill(value)
                            filled_fields.append(field_name)
                            field_filled = True
                            break
                    
                    if not field_filled:
                        logger.warning(f"Could not find field: {field_name}")
                        
                except Exception as e:
                    logger.warning(f"Error filling field {field_name}: {e}")
            
            # Submit form if button selector provided
            if submit_button_selector:
                try:
                    submit_button = await page.query_selector(submit_button_selector)
                    if submit_button:
                        await submit_button.click()
                        await page.wait_for_load_state("networkidle")
                except Exception as e:
                    logger.warning(f"Error submitting form: {e}")
            
            # Get page content after form submission
            content = await page.content()
            await page.close()
            
            return json.dumps({
                "success": True,
                "filled_fields": filled_fields,
                "message": f"Successfully filled {len(filled_fields)} fields",
                "page_title": await page.title() if not page.is_closed() else "Unknown"
            }, indent=2)
            
        except Exception as e:
            logger.error(f"Form filling error: {e}")
            return f"Error filling form: {str(e)}"

class WebScrapingTool(PlaywrightWebTool):
    """Tool for scraping data from web pages."""

    name: str = "web_scrape"
    description: str = "Scrape specific data from web pages using CSS selectors"
    args_schema: type = WebScrapingInput
    
    def _run(self, url: str, selectors: Dict[str, str], wait_for_selector: Optional[str] = None) -> str:
        """Synchronous wrapper for async scraping."""
        return asyncio.run(self._async_run(url, selectors, wait_for_selector))
    
    async def _async_run(self, url: str, selectors: Dict[str, str], wait_for_selector: Optional[str] = None) -> str:
        """Scrape data from a web page."""
        try:
            browser = await self._get_browser()
            page = await browser.new_page()
            
            await page.goto(url)
            
            # Wait for specific selector if provided
            if wait_for_selector:
                await page.wait_for_selector(wait_for_selector, timeout=self.timeout)
            else:
                await page.wait_for_load_state("networkidle")
            
            # Extract data using provided selectors
            scraped_data = {}
            for data_key, selector in selectors.items():
                try:
                    elements = await page.query_selector_all(selector)
                    if elements:
                        if len(elements) == 1:
                            # Single element
                            text = await elements[0].inner_text()
                            scraped_data[data_key] = text.strip()
                        else:
                            # Multiple elements
                            texts = []
                            for elem in elements:
                                text = await elem.inner_text()
                                texts.append(text.strip())
                            scraped_data[data_key] = texts
                    else:
                        scraped_data[data_key] = None
                        logger.warning(f"No elements found for selector: {selector}")
                        
                except Exception as e:
                    logger.warning(f"Error scraping {data_key}: {e}")
                    scraped_data[data_key] = None
            
            await page.close()
            
            return json.dumps({
                "url": url,
                "scraped_data": scraped_data,
                "success": True
            }, indent=2)
            
        except Exception as e:
            logger.error(f"Web scraping error: {e}")
            return f"Error scraping web page: {str(e)}"

# Simple fallback tools when Playwright is not available
class SimpleWebSearchTool(BaseTool):
    """Simple web search tool fallback."""
    name: str = "web_search"
    description: str = "Search the web for information (simplified version)"

    def _run(self, query: str, site: Optional[str] = None, max_results: int = 10) -> str:
        return f"Web search for '{query}' would be performed here. Playwright not available."

class SimpleWebFormTool(BaseTool):
    """Simple web form tool fallback."""
    name: str = "web_form_fill"
    description: str = "Fill web forms (simplified version)"

    def _run(self, url: str, form_data: Dict[str, str], submit_button_selector: Optional[str] = None) -> str:
        return f"Form filling for '{url}' would be performed here. Playwright not available."

class SimpleWebScrapingTool(BaseTool):
    """Simple web scraping tool fallback."""
    name: str = "web_scrape"
    description: str = "Scrape web pages (simplified version)"

    def _run(self, url: str, selectors: Dict[str, str], wait_for_selector: Optional[str] = None) -> str:
        return f"Web scraping for '{url}' would be performed here. Playwright not available."

# Tool instances for use by agents - use simple tools for now
logger.warning("Using simplified web tools for initial implementation")
web_search_tool = SimpleWebSearchTool()
web_form_tool = SimpleWebFormTool()
web_scraping_tool = SimpleWebScrapingTool()
