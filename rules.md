# Expendra Project Rules and Guidelines

This document outlines the rules and guidelines to ensure consistency, quality, and collaboration for the Expendra project. Adherence to these standards is crucial for the project's success and maintainability.

## 1. Project Name and Repository

*   **Project Name:** Expendra
*   **Repository:** `https://github.com/PapaBear1981/Expendra`
    *   All development efforts should be directed towards this repository.

## 2. Coding Standards

### 2.1. Frontend (React/TypeScript)

*   **Language:** Use TypeScript for all frontend code.
*   **Style Guide:** Adhere to the Airbnb JavaScript Style Guide, adapted for TypeScript.
*   **Formatting:** Utilize Prettier for automatic code formatting to maintain a consistent look and feel. Configure Prettier to run on save in IDEs.
*   **Linting:** Employ ESLint with appropriate TypeScript and React plugins for static analysis and identifying potential issues. Configure ESLint rules to enforce best practices.
*   **Component Structure:**
    *   Adopt a component-based architecture with clear separation of concerns.
    *   Organize components logically (e.g., by feature, type, or using Atomic Design principles).
    *   Ensure components are reusable and maintainable.
*   **State Management:** Use React's built-in state management (useState, useContext) or a dedicated library (e.g., Zustand, Redux (if complexity demands)) judiciously.
*   **Typing:** Apply TypeScript types rigorously to all props, state, context, and API responses.
*   **Error Handling:** Implement robust error handling for API calls, user inputs, and asynchronous operations. Display user-friendly error messages.

### 2.2. Backend (Python/FastAPI)

*   **Language:** Exclusively use Python 3.10 or later.
*   **Style Guide:** Follow PEP 8 for general Python code style.
*   **Formatting:** Use Black for automatic code formatting.
*   **Linting:** Employ Flake8 or Ruff for static analysis and adherence to PEP 8.
*   **Type Hinting:** Apply type hints (PEP 484) to all function signatures, return values, and complex data structures.
*   **API Design:**
    *   Design RESTful APIs using FastAPI, ensuring clear endpoint definitions.
    *   Utilize Pydantic models for request and response validation.
    *   Generate and maintain OpenAPI (Swagger UI) documentation for all API endpoints.
*   **Database Interaction:**
    *   Use SQLAlchemy as the Object-Relational Mapper (ORM) for PostgreSQL.
    *   Define clear models and relationships for database entities.
    *   Implement efficient database queries.
*   **Asynchronous Operations:** Leverage Python's `async`/`await` syntax for I/O-bound operations such as LLM calls, web scraping, and database queries to maximize performance.
*   **Security:**
    *   Never hardcode sensitive information (API keys, database credentials). Use environment variables and secrets management tools (e.g., AWS Secrets Manager).
    *   Implement secure authentication mechanisms (e.g., JWT).
    *   Sanitize all user inputs to prevent injection attacks.

### 2.3. AI Agent Framework (CrewAI/LangChain)

*   **Agent Definition:**
    *   Define agents with clear roles, specific goals, and brief backstories to guide their behavior.
    *   Ensure agents are designed for collaboration.
*   **Task Management:**
    *   Break down complex research or sourcing tasks into smaller, actionable sub-tasks.
    *   Assign tasks to the most appropriate agent.
*   **Tool Usage:**
    *   Develop and document tools used by agents (e.g., web scrapers, data parsers, LLM wrappers).
    *   Ensure tools are robust and handle potential errors gracefully.
*   **Memory:**
    *   Implement conversational memory using LangChain's memory components to maintain context across multi-turn interactions.
    *   Consider if specific findings need to be persisted in the main database.
*   **Error Handling:** Implement comprehensive error handling within agent workflows and task execution to gracefully manage failures and provide informative feedback.

## 3. Collaboration & Workflow

*   **Version Control:** Git is mandatory. All code changes must be committed and pushed to feature branches.
*   **Branching Strategy:** Adopt the Gitflow branching model:
    *   `main`: Production-ready code.
    *   `develop`: Latest stable code for the next release.
    *   `feature/*`: For new features (e.g., `feature/add-search-ui`).
    *   `bugfix/*`: For fixing bugs (e.g., `bugfix/fix-login-error`).
    *   `release/*`: For release preparation.
*   **Pull Requests (PRs):**
    *   All code changes must be submitted via Pull Requests targeting the `develop` branch.
    *   Each PR should have a clear title and description, referencing the relevant issue or task.
    *   Mandatory: At least one other team member must review and approve a PR before merging.
*   **Commit Messages:** Adhere to Conventional Commits for all commit messages:
    *   `feat: <description>` - for new features
    *   `fix: <description>` - for bug fixes
    *   `docs: <description>` - for documentation changes
    *   `style: <description>` - for code style changes that do not affect functionality
    *   `refactor: <description>` - for code changes that neither fix a bug nor add a feature
    *   `chore: <description>` - for maintenance tasks, build process updates, etc.
    *   `test: <description>` - for adding missing tests or correcting existing tests

## 4. Architectural Principles

*   **Modularity:** Design components and services to be loosely coupled and highly cohesive. Aim for single responsibility for modules and classes.
*   **Separation of Concerns:** Maintain a clear distinction between frontend, backend API services, AI orchestration logic, and data storage.
*   **Extensibility:** Design the system with future growth in mind. New LLM providers, data sources, or agent types should be relatively easy to integrate.
*   **Scalability:** Architect backend services and database interactions with scalability in mind for eventual deployment on AWS.
*   **Security:** Security is paramount. Implement security best practices at all layers, from user authentication to data handling and deployment.

## 5. Documentation Standards

*   **Code Comments:** Write clear, concise comments for complex logic, non-obvious code paths, and any public interfaces (functions, classes, methods).
*   **API Documentation:** FastAPI will auto-generate Swagger UI documentation. Ensure that the descriptions within Pydantic models and endpoint functions are comprehensive and accurate.
*   **Project Documentation:**
    *   **`README.md`:** Provide a high-level overview of the project, setup instructions, how to run the application locally, and contribution guidelines.
    *   **`PRD.md`:** A detailed Product Requirements Document outlining features, user stories, and business objectives.
    *   **`rules.md`:** This document, detailing standards and guidelines.
    *   **Diagrams:** Include architectural diagrams (e.g., using Mermaid) in relevant markdown files (`README.md` or a dedicated `docs/architecture.md`) to illustrate system design.