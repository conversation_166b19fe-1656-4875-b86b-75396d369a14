import { extendTheme, type ThemeConfig } from '@chakra-ui/react'

// Define the theme configuration
const config: ThemeConfig = {
  initialColorMode: 'dark',
  useSystemColorMode: false,
}

// Enhanced color palette inspired by modern dashboard designs
const colors = {
  brand: {
    50: '#e6f3ff',
    100: '#b3d9ff',
    200: '#80bfff',
    300: '#4da6ff',
    400: '#1a8cff',
    500: '#0066cc', // Primary brand color
    600: '#0052a3',
    700: '#003d7a',
    800: '#002952',
    900: '#001429',
  },
  // Enhanced accent colors for data visualization
  accent: {
    purple: '#8B5CF6',
    blue: '#3B82F6',
    green: '#10B981',
    yellow: '#F59E0B',
    red: '#EF4444',
    orange: '#F97316',
    pink: '#EC4899',
    cyan: '#06B6D4',
  },
  // Gradient colors for modern effects
  gradient: {
    primary: 'linear(to-r, #667eea, #764ba2)',
    success: 'linear(to-r, #11998e, #38ef7d)',
    warning: 'linear(to-r, #f093fb, #f5576c)',
    info: 'linear(to-r, #4facfe, #00f2fe)',
    purple: 'linear(to-r, #a8edea, #fed6e3)',
    blue: 'linear(to-r, #667eea, #764ba2)',
  },
  gray: {
    50: '#f7fafc',
    100: '#edf2f7',
    200: '#e2e8f0',
    300: '#cbd5e0',
    400: '#a0aec0',
    500: '#718096',
    600: '#4a5568',
    700: '#2d3748',
    800: '#1a202c',
    900: '#0f1419', // Darker for better contrast
  }
}

// Enhanced component styles for modern dashboard
const components = {
  Button: {
    baseStyle: {
      fontWeight: 'semibold',
      borderRadius: 'lg',
      transition: 'all 0.2s ease-in-out',
    },
    variants: {
      solid: {
        bg: 'brand.500',
        color: 'white',
        _hover: {
          bg: 'brand.600',
          transform: 'translateY(-2px)',
          boxShadow: '0 8px 25px rgba(0, 102, 204, 0.3)',
        },
        _active: {
          bg: 'brand.700',
          transform: 'translateY(0)',
        },
      },
      gradient: {
        bgGradient: 'gradient.primary',
        color: 'white',
        _hover: {
          transform: 'translateY(-2px)',
          boxShadow: '0 8px 25px rgba(102, 126, 234, 0.4)',
        },
        _active: {
          transform: 'translateY(0)',
        },
      },
      ghost: {
        _hover: {
          bg: 'gray.700',
          transform: 'translateY(-1px)',
        },
      },
    },
  },
  Input: {
    variants: {
      filled: {
        field: {
          bg: 'gray.700',
          border: '1px solid',
          borderColor: 'gray.600',
          _hover: {
            bg: 'gray.600',
            borderColor: 'gray.500',
          },
          _focus: {
            bg: 'gray.600',
            borderColor: 'brand.500',
            boxShadow: '0 0 0 1px var(--chakra-colors-brand-500)',
          },
        },
      },
    },
    defaultProps: {
      variant: 'filled',
    },
  },
  Card: {
    baseStyle: {
      container: {
        bg: 'gray.800',
        borderRadius: 'xl',
        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08)',
        border: '1px solid',
        borderColor: 'gray.700',
        transition: 'all 0.2s ease-in-out',
        _hover: {
          transform: 'translateY(-2px)',
          boxShadow: '0 8px 25px rgba(0, 0, 0, 0.15), 0 4px 10px rgba(0, 0, 0, 0.1)',
          borderColor: 'gray.600',
        },
      },
    },
    variants: {
      elevated: {
        container: {
          bg: 'gray.800',
          boxShadow: '0 10px 40px rgba(0, 0, 0, 0.2)',
          border: '1px solid',
          borderColor: 'gray.600',
        },
      },
      gradient: {
        container: {
          bgGradient: 'linear(135deg, gray.800 0%, gray.700 100%)',
          border: '1px solid',
          borderColor: 'gray.600',
        },
      },
    },
  },
  Input: {
    variants: {
      filled: {
        field: {
          bg: 'gray.900',
          borderColor: 'gray.700',
          color: 'white',
          _placeholder: {
            color: 'gray.400',
          },
          _hover: {
            bg: 'gray.800',
            borderColor: 'gray.600',
          },
          _focus: {
            bg: 'gray.800',
            borderColor: 'brand.500',
            boxShadow: '0 0 0 1px var(--chakra-colors-brand-500)',
          },
        },
      },
      outline: {
        field: {
          bg: 'gray.900',
          borderColor: 'gray.700',
          color: 'white',
          _placeholder: {
            color: 'gray.400',
          },
          _hover: {
            borderColor: 'gray.600',
          },
          _focus: {
            borderColor: 'brand.500',
            boxShadow: '0 0 0 1px var(--chakra-colors-brand-500)',
          },
        },
      },
    },
    defaultProps: {
      variant: 'filled',
    },
  },
  Textarea: {
    variants: {
      filled: {
        bg: 'gray.900',
        borderColor: 'gray.700',
        color: 'white',
        _placeholder: {
          color: 'gray.400',
        },
        _hover: {
          bg: 'gray.800',
          borderColor: 'gray.600',
        },
        _focus: {
          bg: 'gray.800',
          borderColor: 'brand.500',
          boxShadow: '0 0 0 1px var(--chakra-colors-brand-500)',
        },
      },
      outline: {
        bg: 'gray.900',
        borderColor: 'gray.700',
        color: 'white',
        _placeholder: {
          color: 'gray.400',
        },
        _hover: {
          borderColor: 'gray.600',
        },
        _focus: {
          borderColor: 'brand.500',
          boxShadow: '0 0 0 1px var(--chakra-colors-brand-500)',
        },
      },
    },
    defaultProps: {
      variant: 'filled',
    },
  },
  Stat: {
    baseStyle: {
      container: {
        bg: 'transparent',
      },
      label: {
        fontWeight: 'medium',
        fontSize: 'sm',
        color: 'gray.400',
      },
      number: {
        fontSize: '2xl',
        fontWeight: 'bold',
        color: 'gray.100',
      },
      helpText: {
        fontSize: 'xs',
        color: 'gray.500',
      },
    },
  },
}

// Global styles
const styles = {
  global: {
    body: {
      bg: 'gray.900',
      color: 'gray.100',
    },
  },
}

// Font configuration
const fonts = {
  heading: `'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"`,
  body: `'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"`,
}

// Enhanced spacing and sizing
const space = {
  px: '1px',
  0.5: '0.125rem',
  1: '0.25rem',
  1.5: '0.375rem',
  2: '0.5rem',
  2.5: '0.625rem',
  3: '0.75rem',
  3.5: '0.875rem',
  4: '1rem',
  5: '1.25rem',
  6: '1.5rem',
  7: '1.75rem',
  8: '2rem',
  9: '2.25rem',
  10: '2.5rem',
  12: '3rem',
  14: '3.5rem',
  16: '4rem',
  20: '5rem',
  24: '6rem',
  28: '7rem',
  32: '8rem',
  36: '9rem',
  40: '10rem',
  44: '11rem',
  48: '12rem',
  52: '13rem',
  56: '14rem',
  60: '15rem',
  64: '16rem',
  72: '18rem',
  80: '20rem',
  96: '24rem',
}

// Enhanced shadows for depth
const shadows = {
  xs: '0 0 0 1px rgba(0, 0, 0, 0.05)',
  sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  base: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
  md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
  outline: '0 0 0 3px rgba(66, 153, 225, 0.6)',
  inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
  none: 'none',
  // Custom dashboard shadows
  glow: '0 0 20px rgba(102, 126, 234, 0.3)',
  'glow-lg': '0 0 40px rgba(102, 126, 234, 0.2)',
}

const theme = extendTheme({
  config,
  colors,
  components,
  styles,
  fonts,
  space,
  shadows,
})

export default theme
